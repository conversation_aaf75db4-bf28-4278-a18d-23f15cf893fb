import { Modu<PERSON> } from '@nestjs/common';
import { StoreController } from './store.controller';
import { StoreService } from './store.service';
import { GroupedProductsController } from './grouped-products.controller';
import { GroupedProductsService } from './grouped-products.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [StoreController, GroupedProductsController],
  providers: [
    GroupedProductsService,
    StoreService
  ],
  exports: [StoreService, GroupedProductsService],
})
export class StoreModule {}


