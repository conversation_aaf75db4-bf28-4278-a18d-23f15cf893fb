import { Controller, Get, Post, Body, Patch, Param, Delete, ParseIntPipe, UseGuards, Query } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { StoreService } from './store.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateMainCategoryDto } from './dto/create-main-category.dto';
import { UpdateMainCategoryDto } from './dto/update-main-category.dto';
import { AddCategoriesToMainCategoryDto } from './dto/add-categories-to-main-category.dto';
import { Category, Product } from '@prisma/client';
import { PaginationQueryDto } from './dto/pagination-query.dto';

@Controller('store')
export class StoreController {
  constructor(private readonly storeService: StoreService) {}

  // Category Management Endpoints
  @Post('categories')
  createCategory(@Body() createCategoryDto: CreateCategoryDto): Promise<Category> {
    return this.storeService.createCategory(createCategoryDto);
  }

  @Get('categories')
  getAllCategories(): Promise<Category[]> {
    return this.storeService.getAllCategories();
  }

  @Get('categories/:id')
  getCategoryById(@Param('id', ParseIntPipe) id: number): Promise<Category> {
    return this.storeService.getCategoryById(id);
  }

  @Patch('categories/:id')
  updateCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<Category> {
    return this.storeService.updateCategory(id, updateCategoryDto);
  }

  @Delete('categories/:id')
  deleteCategory(@Param('id', ParseIntPipe) id: number): Promise<Category> {
    return this.storeService.deleteCategory(id);
  }

  // MainCategory Management Endpoints
  @Post('main-categories')
  createMainCategory(@Body() createMainCategoryDto: CreateMainCategoryDto) {
    return this.storeService.createMainCategory(createMainCategoryDto);
  }

  @Get('main-categories')
  getAllMainCategories() {
    return this.storeService.getAllMainCategories();
  }

  @Get('main-categories/:id')
  getMainCategoryById(@Param('id', ParseIntPipe) id: number) {
    return this.storeService.getMainCategoryById(id);
  }

  @Patch('main-categories/:id')
  updateMainCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateMainCategoryDto: UpdateMainCategoryDto,
  ) {
    return this.storeService.updateMainCategory(id, updateMainCategoryDto);
  }

  @Post('main-categories/:id/categories')
  addCategoriesToMainCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() addCategoriesDto: AddCategoriesToMainCategoryDto,
  ) {
    return this.storeService.addCategoriesToMainCategory(id, addCategoriesDto);
  }

  @Delete('main-categories/:id')
  deleteMainCategory(@Param('id', ParseIntPipe) id: number) {
    return this.storeService.deleteMainCategory(id);
  }

  // Product Management Endpoints
  @Post('products')
  createProduct(@Body() createProductDto: CreateProductDto): Promise<Product> {
    return this.storeService.createProduct(createProductDto);
  }

  @Post('categories/:categoryId/products')
  createProductForCategory(
    @Param('categoryId', ParseIntPipe) categoryId: number,
    @Body() createProductDto: CreateProductDto
  ): Promise<Product> {
    // Ensure the product is associated with the specified category
    const productWithCategory = {
      ...createProductDto,
      categoryIds: [...(createProductDto.categoryIds || []), categoryId]
    };
    return this.storeService.createProduct(productWithCategory);
  }

  @Get('products')
  getAllProducts(@Query() paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 20, search } = paginationQuery;
    return this.storeService.getAllProducts(page, limit, search);
  }

  @Get('products/:id')
  getProductById(@Param('id', ParseIntPipe) id: number): Promise<Product> {
    return this.storeService.getProductById(id);
  }

  @Get('categories/:categoryId/products')
  getProductsByCategory(
    @Param('categoryId', ParseIntPipe) categoryId: number,
    @Query() paginationQuery: PaginationQueryDto
  ) {
    const { page = 1, limit = 20, search } = paginationQuery;
    return this.storeService.getProductsByCategory(categoryId, page, limit, search);
  }

  @Patch('products/:id')
  updateProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductDto: UpdateProductDto,
  ): Promise<Product> {
    return this.storeService.updateProduct(id, updateProductDto);
  }
  // @UseGuards(JwtAuthGuard, RolesGuard)
  // @Roles('superAdmin')
  @Delete('products/:id')
  deleteProduct(@Param('id', ParseIntPipe) id: number): Promise<Product> {
    return this.storeService.deleteProduct(id);
  }
}


