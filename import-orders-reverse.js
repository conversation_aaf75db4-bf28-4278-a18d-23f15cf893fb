const fs = require('fs');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['error'],
  errorFormat: 'minimal'
});

// Function to parse CSV content manually (handles quoted CSV)
function parseCSV(content) {
  const lines = content.split('\n');
  const rows = [];
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim() === '') continue;
    
    // Parse CSV line with quoted values
    const values = [];
    let current = '';
    let inQuotes = false;
    
    for (let j = 0; j < lines[i].length; j++) {
      const char = lines[i][j];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    values.push(current.trim()); // Add the last value
    
    if (i === 0) {
      // Store headers for reference
      rows.headers = values;
    } else {
      // Create object with headers as keys
      const row = {};
      rows.headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      rows.push(row);
    }
  }
  
  return rows;
}

// Function to parse date
function parseDate(dateString) {
  if (!dateString || dateString.trim() === '') {
    return null;
  }
  
  try {
    const date = new Date(dateString.trim());
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    return null;
  }
}

// Sleep function for rate limiting
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function importOrdersReverse() {
  console.log('🔄 Starting REVERSE order import (last to first)...');

  try {
    // Read both CSV files
    console.log('📖 Reading CSV files...');
    const orderProductContent = fs.readFileSync('D1Dka_wc_order_product_lookup.csv', 'utf8');
    const orderProductRows = parseCSV(orderProductContent);
    
    const orderItemsContent = fs.readFileSync('D1Dka_woocommerce_order_items.csv', 'utf8');
    const orderItemsRows = parseCSV(orderItemsContent);
    
    console.log(`📊 Found ${orderProductRows.length} rows in order product lookup`);
    console.log(`📊 Found ${orderItemsRows.length} rows in order items`);

    // Create maps for efficient lookup
    const orderItemsMap = new Map();
    orderItemsRows.forEach(row => {
      if (row.order_item_type === 'line_item') {
        orderItemsMap.set(row.order_item_id, row);
      }
    });

    // Group order product data by order_id (REVERSE ORDER)
    const ordersByOrderId = new Map();
    
    // Process in REVERSE order
    for (let i = orderProductRows.length - 1; i >= 0; i--) {
      const row = orderProductRows[i];
      
      try {
        const orderId = parseInt(row.order_id);
        const orderItemId = row.order_item_id;
        const customerId = parseInt(row.customer_id);
        const dateCreated = parseDate(row.date_created);
        const quantity = parseInt(row.product_qty) || 1;
        const netRevenue = parseFloat(row.product_net_revenue) || 0;
        const grossRevenue = parseFloat(row.product_gross_revenue) || 0;
        const couponAmount = parseFloat(row.coupon_amount) || 0;
        const taxAmount = parseFloat(row.tax_amount) || 0;
        const shippingAmount = parseFloat(row.shipping_amount) || 0;
        const shippingTaxAmount = parseFloat(row.shipping_tax_amount) || 0;

        // Get item name from the other table
        const orderItemData = orderItemsMap.get(orderItemId);
        const itemName = orderItemData ? orderItemData.order_item_name : null;

        if (!itemName) continue;

        // Initialize order if not exists
        if (!ordersByOrderId.has(orderId)) {
          ordersByOrderId.set(orderId, {
            id: orderId,
            customerId: customerId,
            name: `Order #${orderId}`,
            email: '',
            dateCreated: dateCreated,
            couponAmount: 0,
            taxAmount: 0,
            shippingAmount: 0,
            shippingTaxAmount: 0,
            totalAmount: 0,
            status: 'COMPLETED',
            items: []
          });
        }

        const order = ordersByOrderId.get(orderId);
        
        // Add item to order
        order.items.push({
          quantity: quantity,
          productNetRevenue: netRevenue,
          productGrossRevenue: grossRevenue,
          itemName: itemName,
          itemType: 'line_item'
        });

        // Update order totals
        order.couponAmount += couponAmount;
        order.taxAmount += taxAmount;
        order.shippingAmount += shippingAmount;
        order.shippingTaxAmount += shippingTaxAmount;
        order.totalAmount += grossRevenue;

      } catch (error) {
        console.log(`⚠️  Error processing row ${i}: ${error.message}`);
      }
    }

    // Convert to array and sort by order ID (highest first)
    const orders = Array.from(ordersByOrderId.values()).sort((a, b) => b.id - a.id);

    console.log(`📊 Processing complete: ${orders.length} orders to process (reverse order)`);

    // Get existing users
    console.log('📋 Fetching existing users...');
    const users = await prisma.user.findMany({
      select: { id: true, email: true, name: true }
    });
    const userMap = new Map(users.map(user => [user.id, user]));
    console.log(`📋 Found ${users.length} users in database`);

    // Check which orders already exist
    console.log('🔍 Checking existing orders...');
    const existingOrderIds = new Set();
    const orderIds = orders.map(o => o.id);
    
    // Check in batches to avoid memory issues
    const batchSize = 1000;
    for (let i = 0; i < orderIds.length; i += batchSize) {
      const batch = orderIds.slice(i, i + batchSize);
      const existing = await prisma.order.findMany({
        where: { id: { in: batch } },
        select: { id: true }
      });
      existing.forEach(order => existingOrderIds.add(order.id));
    }

    const ordersToProcess = orders.filter(order => !existingOrderIds.has(order.id));
    console.log(`📊 Orders already exist: ${existingOrderIds.size}`);
    console.log(`📊 Orders to import: ${ordersToProcess.length}`);

    if (ordersToProcess.length === 0) {
      console.log('✅ All orders already imported!');
      return;
    }

    // Import orders with better error handling
    console.log('\n💾 Starting database import (reverse order)...');
    let successCount = 0;
    let failCount = 0;
    let linkedOrdersCount = 0;
    let unlinkedOrdersCount = 0;
    const failedOrders = [];

    for (let i = 0; i < ordersToProcess.length; i++) {
      const orderData = ordersToProcess[i];
      
      try {
        // Check if customer exists
        const customer = userMap.get(orderData.customerId);
        
        let orderName, orderEmail, customerId;
        
        if (customer) {
          orderName = customer.name;
          orderEmail = customer.email;
          customerId = orderData.customerId;
          linkedOrdersCount++;
        } else {
          orderName = `Customer ${orderData.customerId}`;
          orderEmail = `customer${orderData.customerId}@legacy.com`;
          customerId = null;
          unlinkedOrdersCount++;
        }

        // Create order with retry logic
        let retries = 3;
        let created = false;
        
        while (retries > 0 && !created) {
          try {
            await prisma.order.create({
              data: {
                id: orderData.id,
                customerId: customerId,
                name: orderName,
                email: orderEmail,
                dateCreated: orderData.dateCreated || new Date(),
                couponAmount: orderData.couponAmount,
                taxAmount: orderData.taxAmount,
                shippingAmount: orderData.shippingAmount,
                shippingTaxAmount: orderData.shippingTaxAmount,
                totalAmount: orderData.totalAmount,
                status: orderData.status,
                items: {
                  create: orderData.items
                }
              }
            });
            
            created = true;
            successCount++;
            
            if (successCount % 100 === 0) {
              console.log(`✅ Progress: ${successCount}/${ordersToProcess.length} orders imported`);
            }
            
          } catch (error) {
            retries--;
            if (error.message.includes("Can't reach database")) {
              console.log(`🔄 Database connection issue, retrying... (${retries} left)`);
              await sleep(2000); // Wait 2 seconds before retry
            } else {
              throw error; // Re-throw non-connection errors
            }
          }
        }
        
        if (!created) {
          throw new Error('Failed after 3 retries');
        }

        // Rate limiting - small delay every 50 orders
        if (successCount % 50 === 0) {
          await sleep(100);
        }

      } catch (error) {
        failCount++;
        failedOrders.push({ orderId: orderData.id, error: error.message });
        console.log(`❌ Failed to create order #${orderData.id}: ${error.message}`);
      }
    }

    console.log(`\n🎉 Reverse import complete:`);
    console.log(`   - Successfully imported: ${successCount}`);
    console.log(`   - Failed: ${failCount}`);
    console.log(`   - Orders linked to customers: ${linkedOrdersCount}`);
    console.log(`   - Orders without customer link: ${unlinkedOrdersCount}`);
    
    if (failedOrders.length > 0) {
      console.log(`\n📝 Failed orders saved to failed_orders.txt`);
      fs.writeFileSync('failed_orders.txt', 
        failedOrders.map(f => `${f.orderId}: ${f.error}`).join('\n')
      );
    }

  } catch (error) {
    console.error('❌ Import failed:', error);
  }
}

// Run the import
async function main() {
  try {
    await importOrdersReverse();
  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
