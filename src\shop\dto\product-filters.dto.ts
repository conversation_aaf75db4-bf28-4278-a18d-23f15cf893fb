import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsBoolean, <PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export enum SortBy {
  PRICE_LOW_TO_HIGH = 'price_asc',
  PRICE_HIGH_TO_LOW = 'price_desc',
  NEWEST_FIRST = 'newest',
  OLDEST_FIRST = 'oldest',
  NAME_A_TO_Z = 'name_asc',
  NAME_Z_TO_A = 'name_desc',
  POPULARITY = 'popularity',
  RATING = 'rating',
  STOCK_STATUS = 'stock_status'
}

export enum StockStatus {
  IN_STOCK = 'IN_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  ON_BACKORDER = 'ON_BACKORDER'
}

export enum ProductType {
  SIMPLE = 'SIMPLE',
  VARIABLE = 'VARIABLE',
  GROUPED = 'GROUPED',
  EXTERNAL = 'EXTERNAL'
}

export class ProductFiltersDto {
  // Pagination
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  limit?: number = 20;

  // Price filters
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  minPrice?: number;

  @IsOptional()
  @Type(() => Number)
  @Min(0)
  maxPrice?: number;

  // Sale filter
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onSale?: boolean;

  // Stock filters
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  inStock?: boolean;

  @IsOptional()
  @IsEnum(StockStatus)
  stockStatus?: StockStatus;

  @IsOptional()
  @Type(() => Number)
  @Min(0)
  minStock?: number;

  @IsOptional()
  @Type(() => Number)
  @Min(0)
  maxStock?: number;

  // Product type filter
  @IsOptional()
  @IsEnum(ProductType)
  productType?: ProductType;

  // Category filters
  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  @IsArray()
  @Type(() => Number)
  categoryIds?: number[];

  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  @IsArray()
  @IsString({ each: true })
  categorySlugs?: string[];

  // Tag filters
  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  @IsArray()
  @Type(() => Number)
  tagIds?: number[];

  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  @IsArray()
  @IsString({ each: true })
  tagNames?: string[];

  // Date filters
  @IsOptional()
  @Transform(({ value }) => new Date(value))
  createdAfter?: Date;

  @IsOptional()
  @Transform(({ value }) => new Date(value))
  createdBefore?: Date;

  // Search
  @IsOptional()
  @IsString()
  search?: string;

  // SKU filter
  @IsOptional()
  @IsString()
  sku?: string;

  // Sorting
  @IsOptional()
  @IsEnum(SortBy)
  sortBy?: SortBy = SortBy.NEWEST_FIRST;

  // Featured products
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  featured?: boolean;

  // Discount percentage filter
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(100)
  minDiscountPercent?: number;

  // Price range presets
  @IsOptional()
  @IsString()
  priceRange?: string; // e.g., "0-50", "50-100", "100-200", "200+"
}

export interface PriceRange {
  min?: number;
  max?: number;
}

export interface FilterSummary {
  totalProducts: number;
  priceRange: {
    min: number;
    max: number;
  };
  availableCategories: Array<{
    id: number;
    name: string;
    slug: string;
    count: number;
  }>;
  availableTags: Array<{
    id: number;
    name: string;
    slug: string;
    count: number;
  }>;
  stockStatusCounts: {
    inStock: number;
    outOfStock: number;
    onBackorder: number;
  };
  productTypeCounts: {
    simple: number;
    variable: number;
    grouped: number;
    external: number;
  };
  onSaleCount: number;
}
