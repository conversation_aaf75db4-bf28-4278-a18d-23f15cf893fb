import { 
  Controller, 
  Get, 
  Param, 
  Query, 
  ParseIntPipe, 
  UseGuards 
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrderQueryDto } from './dto/order-query.dto';
import { 
  OrderDto, 
  PaginatedOrdersDto 
} from './dto/order-response.dto';
import { 
  OrderReportsDto,
  OrderInsightsDto,
  OrderAnalyticsDto
} from './dto/order-analytics.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@Controller('orders')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin', 'super-admin')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Get()
  async getAllOrders(@Query() query: OrderQueryDto): Promise<PaginatedOrdersDto> {
    return this.ordersService.getAllOrders(query);
  }

  @Get('reports')
  async getOrderReports(): Promise<OrderReportsDto> {
    return this.ordersService.getOrderReports();
  }

  @Get('insights')
  async getOrderInsights(
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
  ): Promise<OrderInsightsDto> {
    const fromDate = dateFrom ? new Date(dateFrom) : undefined;
    const toDate = dateTo ? new Date(dateTo) : undefined;
    return this.ordersService.getOrderInsights(fromDate, toDate);
  }

  @Get('analytics/last-day')
  async getLastDayAnalytics(): Promise<OrderAnalyticsDto & { dailyStats: any[] }> {
    return this.ordersService.getLastDayAnalytics();
  }

  @Get('analytics/last-month')
  async getLastMonthAnalytics(): Promise<OrderAnalyticsDto & { dailyStats: any[] }> {
    return this.ordersService.getLastMonthAnalytics();
  }

  @Get('analytics/last-year')
  async getLastYearAnalytics(): Promise<OrderAnalyticsDto & { monthlyStats: any[] }> {
    return this.ordersService.getLastYearAnalytics();
  }

  @Get('analytics/last-5-years')
  async getLast5YearsAnalytics(): Promise<OrderAnalyticsDto & { yearlyStats: any[] }> {
    return this.ordersService.getLast5YearsAnalytics();
  }

  @Get(':id')
  async getOrderById(@Param('id', ParseIntPipe) id: number): Promise<OrderDto> {
    return this.ordersService.getOrderById(id);
  }
}
