const fs = require('fs');
const csv = require('csv-parser');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Function to parse the date format from CSV (7/24/2023 21:24)
function parseDate(dateString) {
  if (!dateString || dateString.trim() === '') {
    return null;
  }
  
  try {
    const cleanedDate = dateString.trim();
    const date = new Date(cleanedDate);
    
    if (isNaN(date.getTime())) {
      console.log(`Invalid date format: ${dateString}`);
      return null;
    }
    
    return date;
  } catch (error) {
    console.log(`Error parsing date: ${dateString}`, error.message);
    return null;
  }
}

// Function to parse CSV content manually (handles quoted CSV)
function parseCSV(content) {
  const lines = content.split('\n');
  const rows = [];

  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim() === '') continue;

    // Parse CSV line with quoted values
    const values = [];
    let current = '';
    let inQuotes = false;

    for (let j = 0; j < lines[i].length; j++) {
      const char = lines[i][j];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    values.push(current.trim()); // Add the last value

    if (i === 0) {
      // Store headers for reference
      rows.headers = values;
    } else {
      // Create object with headers as keys
      const row = {};
      rows.headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      rows.push(row);
    }
  }

  return rows;
}

async function importOrders() {
  console.log('🚀 Starting order import from CSV files...');

  try {
    // Read both CSV files
    console.log('📖 Reading order product lookup CSV...');
    const orderProductContent = fs.readFileSync('D1Dka_wc_order_product_lookup.csv', 'utf8');
    const orderProductRows = parseCSV(orderProductContent);
    
    console.log('📖 Reading order items CSV...');
    const orderItemsContent = fs.readFileSync('D1Dka_woocommerce_order_items.csv', 'utf8');
    const orderItemsRows = parseCSV(orderItemsContent);
    
    console.log(`📊 Found ${orderProductRows.length} rows in order product lookup`);
    console.log(`📊 Found ${orderItemsRows.length} rows in order items`);

    // Create maps for efficient lookup
    const orderItemsMap = new Map();
    orderItemsRows.forEach(row => {
      if (row.order_item_type === 'line_item') {
        orderItemsMap.set(row.order_item_id, row);
      }
    });

    // Group order product data by order_id
    const ordersByOrderId = new Map();
    const errors = [];
    let processedCount = 0;

    for (const row of orderProductRows) {
      try {
        processedCount++;
        
        const orderId = parseInt(row.order_id);
        const orderItemId = row.order_item_id;
        const customerId = parseInt(row.customer_id);
        const dateCreated = parseDate(row.date_created);
        const quantity = parseInt(row.product_qty) || 1;
        const netRevenue = parseFloat(row.product_net_revenue) || 0;
        const grossRevenue = parseFloat(row.product_gross_revenue) || 0;
        const couponAmount = parseFloat(row.coupon_amount) || 0;
        const taxAmount = parseFloat(row.tax_amount) || 0;
        const shippingAmount = parseFloat(row.shipping_amount) || 0;
        const shippingTaxAmount = parseFloat(row.shipping_tax_amount) || 0;

        // Get item name from the other table
        const orderItemData = orderItemsMap.get(orderItemId);
        const itemName = orderItemData ? orderItemData.order_item_name : null;

        if (!itemName) {
          console.log(`⚠️  No item name found for order_item_id ${orderItemId}, skipping`);
          continue;
        }

        // Initialize order if not exists
        if (!ordersByOrderId.has(orderId)) {
          ordersByOrderId.set(orderId, {
            id: orderId,
            customerId: customerId,
            name: `Order #${orderId}`,
            email: '', // Will be filled from customer data if available
            dateCreated: dateCreated,
            couponAmount: 0,
            taxAmount: 0,
            shippingAmount: 0,
            shippingTaxAmount: 0,
            totalAmount: 0,
            status: 'COMPLETED',
            items: []
          });
        }

        const order = ordersByOrderId.get(orderId);
        
        // Add item to order
        order.items.push({
          quantity: quantity,
          productNetRevenue: netRevenue,
          productGrossRevenue: grossRevenue,
          itemName: itemName,
          itemType: 'line_item'
        });

        // Update order totals (accumulate from all items)
        order.couponAmount += couponAmount;
        order.taxAmount += taxAmount;
        order.shippingAmount += shippingAmount;
        order.shippingTaxAmount += shippingTaxAmount;
        order.totalAmount += grossRevenue;

      } catch (error) {
        errors.push(`Row ${processedCount}: Error processing row - ${error.message}`);
      }
    }

    const orders = Array.from(ordersByOrderId.values());

    console.log(`📊 Processing complete:`);
    console.log(`   - Total rows processed: ${processedCount}`);
    console.log(`   - Orders to import: ${orders.length}`);
    console.log(`   - Errors: ${errors.length}`);

    if (errors.length > 0) {
      console.log('\n❌ Errors found:');
      errors.slice(0, 10).forEach(error => console.log(`   ${error}`));
      if (errors.length > 10) {
        console.log(`   ... and ${errors.length - 10} more errors`);
      }
    }

    if (orders.length === 0) {
      console.log('❌ No valid orders to import');
      return;
    }

    // Get existing users to link customer emails
    console.log('📋 Fetching existing users for customer linking...');
    const users = await prisma.user.findMany({
      select: { id: true, email: true, name: true }
    });
    const userMap = new Map(users.map(user => [user.id, user]));
    console.log(`📋 Found ${users.length} users in database`);

    // Analyze customer ID coverage
    const uniqueCustomerIds = new Set(orders.map(order => order.customerId));
    const foundCustomers = Array.from(uniqueCustomerIds).filter(id => userMap.has(id));
    const missingCustomers = Array.from(uniqueCustomerIds).filter(id => !userMap.has(id));

    console.log(`📊 Customer Analysis:`);
    console.log(`   - Unique customers in orders: ${uniqueCustomerIds.size}`);
    console.log(`   - Customers found in database: ${foundCustomers.length}`);
    console.log(`   - Customers missing from database: ${missingCustomers.length}`);
    console.log(`   - Coverage: ${((foundCustomers.length / uniqueCustomerIds.size) * 100).toFixed(1)}%`);

    if (missingCustomers.length > 0) {
      console.log(`\n⚠️  Sample missing customer IDs: ${missingCustomers.slice(0, 10).join(', ')}${missingCustomers.length > 10 ? '...' : ''}`);
    }

    // First, update existing orders that don't have customer links
    console.log('\n🔗 Updating existing orders to link customers...');
    const existingOrders = await prisma.order.findMany({
      where: { customerId: null },
      select: { id: true, name: true }
    });

    let linkedCount = 0;
    for (const existingOrder of existingOrders) {
      // Extract customer ID from order name pattern "Customer XXXX"
      const customerIdMatch = existingOrder.name.match(/Customer (\d+)/);
      if (customerIdMatch) {
        const customerId = parseInt(customerIdMatch[1]);
        const customer = userMap.get(customerId);

        if (customer) {
          try {
            await prisma.order.update({
              where: { id: existingOrder.id },
              data: {
                customerId: customerId,
                name: customer.name,
                email: customer.email
              }
            });
            linkedCount++;
            console.log(`🔗 Linked order #${existingOrder.id} to user ${customer.name}`);
          } catch (error) {
            console.log(`❌ Failed to link order #${existingOrder.id}: ${error.message}`);
          }
        }
      }
    }
    console.log(`✅ Successfully linked ${linkedCount} existing orders to customers`);

    // Import new orders to database (create all orders, link customers when possible)
    console.log('\n💾 Starting database import for new orders...');
    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;
    let linkedOrdersCount = 0;
    let unlinkedOrdersCount = 0;
    const missingCustomerIds = new Set();

    for (const orderData of orders) {
      try {
        // Check if order already exists
        const existingOrder = await prisma.order.findUnique({
          where: { id: orderData.id }
        });

        if (existingOrder) {
          skippedCount++;
          continue; // Skip silently to reduce log spam
        }

        // Check if customer exists in database
        const customer = userMap.get(orderData.customerId);

        let orderName, orderEmail, customerId;

        if (customer) {
          // Customer found - link properly
          orderName = customer.name;
          orderEmail = customer.email;
          customerId = orderData.customerId;
          linkedOrdersCount++;
        } else {
          // Customer not found - create with placeholder info
          orderName = `Customer ${orderData.customerId}`;
          orderEmail = `customer${orderData.customerId}@legacy.com`;
          customerId = null;
          unlinkedOrdersCount++;
          missingCustomerIds.add(orderData.customerId);
        }

        // Create order with items
        await prisma.order.create({
          data: {
            id: orderData.id,
            customerId: customerId,
            name: orderName,
            email: orderEmail,
            dateCreated: orderData.dateCreated || new Date(),
            couponAmount: orderData.couponAmount,
            taxAmount: orderData.taxAmount,
            shippingAmount: orderData.shippingAmount,
            shippingTaxAmount: orderData.shippingTaxAmount,
            totalAmount: orderData.totalAmount,
            status: orderData.status,
            items: {
              create: orderData.items
            }
          }
        });

        successCount++;

        if (customer) {
          console.log(`✅ Created order #${orderData.id} with ${orderData.items.length} items for ${customer.name}`);
        } else {
          console.log(`⚠️  Created order #${orderData.id} with ${orderData.items.length} items (Customer ${orderData.customerId} not found)`);
        }

      } catch (error) {
        failCount++;
        console.log(`❌ Failed to create order #${orderData.id}: ${error.message}`);
      }
    }

    console.log(`\n🎉 Import complete:`);
    console.log(`   - Successfully imported: ${successCount}`);
    console.log(`   - Failed: ${failCount}`);
    console.log(`   - Skipped (already exists): ${skippedCount}`);
    console.log(`   - Orders linked to customers: ${linkedOrdersCount}`);
    console.log(`   - Orders without customer link: ${unlinkedOrdersCount}`);
    console.log(`   - Existing orders linked: ${linkedCount}`);
    console.log(`   - Total processed: ${orders.length}`);

    console.log(`\n📊 Customer Linking Summary:`);
    console.log(`   - Orders with valid customers: ${linkedOrdersCount} (${((linkedOrdersCount / successCount) * 100).toFixed(1)}%)`);
    console.log(`   - Orders with missing customers: ${unlinkedOrdersCount} (${((unlinkedOrdersCount / successCount) * 100).toFixed(1)}%)`);
    console.log(`   - Unique missing customer IDs: ${missingCustomerIds.size}`);

    if (missingCustomerIds.size > 0) {
      const sortedMissingIds = Array.from(missingCustomerIds).sort((a, b) => a - b);
      console.log(`\n🔍 Missing Customer IDs (first 50):`);
      console.log(`   ${sortedMissingIds.slice(0, 50).join(', ')}${sortedMissingIds.length > 50 ? '...' : ''}`);

      if (sortedMissingIds.length > 50) {
        console.log(`\n📝 All missing customer IDs saved to missing_customers.txt`);
        fs.writeFileSync('missing_customers.txt', sortedMissingIds.join('\n'));
      }
    }

  } catch (error) {
    console.error('❌ Import failed:', error);
  }
}

// Run the import
async function main() {
  try {
    await importOrders();
  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
