# Upload API Documentation

## Overview
The Upload API provides endpoints for uploading images to the server and getting back URLs that can be stored in the database. Images are organized into different folders based on their purpose.

## Base URL
```
POST /api/uploads/
```

## Supported File Types
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

## File Size Limit
- Maximum file size: 5MB

## Endpoints

### 1. Upload Product Image
Upload an image for a product.

**Endpoint:** `POST /api/uploads/product-image`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Image file to upload
- `subfolder` (optional): Custom subfolder within products directory

**Example Request:**
```bash
curl -X POST \
  http://localhost:3300/api/uploads/product-image \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/your/image.jpg' \
  -F 'subfolder=featured'
```

**Response:**
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "data": {
    "filename": "file-1703123456789-123456789.jpg",
    "originalName": "product-image.jpg",
    "size": 245760,
    "mimetype": "image/jpeg",
    "url": "http://localhost:3300/api/uploads/products/file-1703123456789-123456789.jpg",
    "path": "./uploads/products/file-1703123456789-123456789.jpg"
  }
}
```

### 2. Upload Variant Image
Upload an image for a product variant.

**Endpoint:** `POST /api/uploads/variant-image`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Image file to upload

**Example Request:**
```bash
curl -X POST \
  http://localhost:3300/api/uploads/variant-image \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/your/variant.jpg'
```

### 3. Upload Category Image
Upload an image for a category.

**Endpoint:** `POST /api/uploads/category-image`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Image file to upload

### 4. Upload Blog Image
Upload an image for a blog post.

**Endpoint:** `POST /api/uploads/blog-image`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Image file to upload

### 5. Upload Multiple Images
Upload multiple images at once.

**Endpoint:** `POST /api/uploads/multiple-images`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `files` (required): Array of image files to upload (max 10 files)
- `subfolder` (optional): Custom subfolder within products directory

**Example Request:**
```bash
curl -X POST \
  http://localhost:3300/api/uploads/multiple-images \
  -H 'Content-Type: multipart/form-data' \
  -F 'files=@/path/to/image1.jpg' \
  -F 'files=@/path/to/image2.jpg' \
  -F 'subfolder=gallery'
```

**Response:**
```json
{
  "success": true,
  "message": "2 images uploaded successfully",
  "data": [
    {
      "filename": "files-1703123456789-123456789.jpg",
      "originalName": "image1.jpg",
      "size": 245760,
      "mimetype": "image/jpeg",
      "url": "http://localhost:3300/api/uploads/products/files-1703123456789-123456789.jpg",
      "path": "./uploads/products/files-1703123456789-123456789.jpg"
    },
    {
      "filename": "files-1703123456790-123456790.jpg",
      "originalName": "image2.jpg",
      "size": 189432,
      "mimetype": "image/jpeg",
      "url": "http://localhost:3300/api/uploads/products/files-1703123456790-123456790.jpg",
      "path": "./uploads/products/files-1703123456790-123456790.jpg"
    }
  ]
}
```

## Directory Structure
```
uploads/
├── products/     # Product images
├── variants/     # Product variant images
├── categories/   # Category images
├── blogs/        # Blog images
└── temp/         # Temporary uploads
```

## Error Responses

### File Too Large
```json
{
  "statusCode": 400,
  "message": "File too large. Maximum size: 5MB",
  "error": "Bad Request"
}
```

### Invalid File Type
```json
{
  "statusCode": 400,
  "message": "Invalid file type. Allowed types: image/jpeg, image/jpg, image/png, image/gif, image/webp",
  "error": "Bad Request"
}
```

### No File Uploaded
```json
{
  "statusCode": 400,
  "message": "No file uploaded",
  "error": "Bad Request"
}
```

## Integration with Product Management

### Using with Product Creation
When creating a product, first upload the image(s), then use the returned URL(s) in your product creation request:

1. Upload image:
```bash
curl -X POST http://localhost:3300/api/uploads/product-image \
  -F 'file=@product.jpg'
```

2. Create product with the returned URL:
```bash
curl -X POST http://localhost:3300/api/store/grouped-products \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Product Name",
    "images": [
      {
        "url": "http://localhost:3300/api/uploads/products/file-1703123456789-123456789.jpg",
        "position": 0
      }
    ]
  }'
```

## Environment Variables
- `BASE_URL`: Base URL for generating image URLs (default: http://localhost:3300)

## Notes
- Images are stored on the server's file system
- Filenames are automatically generated with timestamps to prevent conflicts
- The API returns the full URL that can be directly used in your frontend
- All uploaded files are accessible via HTTP GET requests to the returned URLs
