import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  private readonly uploadPath = './uploads';
  private readonly maxFileSize = 5 * 1024 * 1024; // 5MB
  private readonly allowedMimeTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];

  constructor() {
    this.ensureUploadDirectoryExists();
  }

  private ensureUploadDirectoryExists() {
    const directories = [
      this.uploadPath,
      join(this.uploadPath, 'products'),
      join(this.uploadPath, 'variants'),
      join(this.uploadPath, 'categories'),
      join(this.uploadPath, 'blogs'),
      join(this.uploadPath, 'temp')
    ];

    directories.forEach(dir => {
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
        this.logger.log(`Created directory: ${dir}`);
      }
    });
  }

  getMulterOptions(subfolder: string = 'temp') {
    return {
      storage: diskStorage({
        destination: (_req, _file, cb) => {
          const uploadDir = join(this.uploadPath, subfolder);
          cb(null, uploadDir);
        },
        filename: (_req, file, cb) => {
          // Generate unique filename with timestamp and random string
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          const ext = extname(file.originalname);
          const filename = `${file.fieldname}-${uniqueSuffix}${ext}`;
          cb(null, filename);
        },
      }),
      fileFilter: (_req: any, file: any, cb: any) => {
        if (this.allowedMimeTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new BadRequestException(`Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`), false);
        }
      },
      limits: {
        fileSize: this.maxFileSize,
      },
    };
  }

  generateImageUrl(filename: string, subfolder: string = 'temp'): string {
    const baseUrl = process.env.BASE_URL || 'http://localhost:3300';
    // For static subdomain, serve files directly without /api prefix
    if (baseUrl.includes('static.')) {
      return `${baseUrl}/uploads/${subfolder}/${filename}`;
    }
    // For main domain, keep /api prefix
    return `${baseUrl}/api/uploads/${subfolder}/${filename}`;
  }

  validateImageFile(file: Express.Multer.File): void {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(`Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`);
    }

    if (file.size > this.maxFileSize) {
      throw new BadRequestException(`File too large. Maximum size: ${this.maxFileSize / (1024 * 1024)}MB`);
    }
  }

  getUploadPath(): string {
    return this.uploadPath;
  }
}
