const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['error'],
  errorFormat: 'minimal'
});

// Configuration
const DUPLICATE_THRESHOLD = 5; // Delete if same order pattern appears 5+ times in same day
const DRY_RUN = true; // Set to false to actually delete data
const FOCUS_MONTHS = ['2024-10', '2024-12']; // Focus on these months for analysis
const ANALYZE_ALL_MONTHS = false; // Set to true to analyze all months

// Sleep function for rate limiting
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to format date to YYYY-MM-DD for comparison
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

async function findAndCleanDuplicateOrders() {
  console.log('🔍 Enhanced duplicate order detection...');
  console.log(`📊 Threshold: ${DUPLICATE_THRESHOLD}+ identical orders`);
  console.log(`🔧 Mode: ${DRY_RUN ? 'DRY RUN (no deletions)' : 'LIVE MODE (will delete data)'}`);
  console.log(`📅 Focus months: ${FOCUS_MONTHS.join(', ')}`);

  try {
    // Get orders with date filtering if focusing on specific months
    console.log('📖 Fetching orders...');

    let whereClause = {};
    if (!ANALYZE_ALL_MONTHS && FOCUS_MONTHS.length > 0) {
      // Create date range for focus months
      const dateConditions = FOCUS_MONTHS.map(month => {
        const startDate = new Date(`${month}-01T00:00:00.000Z`);
        const endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + 1);

        return {
          dateCreated: {
            gte: startDate,
            lt: endDate
          }
        };
      });

      whereClause = {
        OR: dateConditions
      };
    }

    const orders = await prisma.order.findMany({
      where: whereClause,
      include: {
        items: {
          select: {
            itemName: true,
            quantity: true,
            productId: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        dateCreated: 'desc'
      }
    });

    console.log(`📊 Found ${orders.length} orders in target period`);

    // Multiple grouping strategies to catch different types of bot behavior
    console.log('\n🔍 Analyzing with multiple detection strategies...');

    // Strategy 1: Exact match (same customer, same day, same items)
    const exactGroups = new Map();

    // Strategy 2: Same customer, same day, regardless of items
    const customerDayGroups = new Map();

    // Strategy 3: Same product pattern, same day (different customers)
    const productDayGroups = new Map();

    for (const order of orders) {
      const date = formatDate(order.dateCreated);
      const customerId = order.customerId || `guest_${order.email}`;

      // Create item signature
      const itemSignature = order.items
        .map(item => `${item.itemName || item.productId || 'unknown'}_${item.quantity}`)
        .sort()
        .join('|');

      // Strategy 1: Exact matching
      const exactKey = `${date}_${customerId}_${itemSignature}`;
      if (!exactGroups.has(exactKey)) {
        exactGroups.set(exactKey, []);
      }
      exactGroups.get(exactKey).push(order);

      // Strategy 2: Same customer, same day (any items)
      const customerDayKey = `${date}_${customerId}`;
      if (!customerDayGroups.has(customerDayKey)) {
        customerDayGroups.set(customerDayKey, []);
      }
      customerDayGroups.get(customerDayKey).push(order);

      // Strategy 3: Same items, same day (any customer)
      const productDayKey = `${date}_${itemSignature}`;
      if (!productDayGroups.has(productDayKey)) {
        productDayGroups.set(productDayKey, []);
      }
      productDayGroups.get(productDayKey).push(order);
    }

    console.log(`📊 Exact patterns: ${exactGroups.size}`);
    console.log(`📊 Customer-day patterns: ${customerDayGroups.size}`);
    console.log(`📊 Product-day patterns: ${productDayGroups.size}`);

    // Find groups with 5+ identical orders
    const duplicateGroups = [];
    let totalDuplicateOrders = 0;
    
    for (const [groupKey, groupOrders] of orderGroups) {
      if (groupOrders.length >= DUPLICATE_THRESHOLD) {
        duplicateGroups.push({
          key: groupKey,
          orders: groupOrders,
          count: groupOrders.length
        });
        totalDuplicateOrders += groupOrders.length;
      }
    }

    console.log(`\n🚨 Found ${duplicateGroups.length} groups with ${DUPLICATE_THRESHOLD}+ identical orders`);
    console.log(`📊 Total duplicate orders: ${totalDuplicateOrders}`);

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate orders found above threshold!');
      return;
    }

    // Sort by count (highest first) to show most problematic groups
    duplicateGroups.sort((a, b) => b.count - a.count);

    // Display details
    console.log('\n📋 Duplicate order groups:');
    for (let i = 0; i < Math.min(duplicateGroups.length, 15); i++) {
      const group = duplicateGroups[i];
      const firstOrder = group.orders[0];
      const date = formatDate(firstOrder.dateCreated);
      
      const customerInfo = firstOrder.customer 
        ? `${firstOrder.customer.name} (${firstOrder.customer.email})`
        : `Guest: ${firstOrder.email}`;
      
      const itemSummary = firstOrder.items
        .map(item => `${item.itemName || 'Product'} (qty: ${item.quantity})`)
        .join(', ');

      const totalValue = group.orders.reduce((sum, order) => sum + parseFloat(order.totalAmount), 0);

      console.log(`\n${i + 1}. 📅 ${date} | 👤 ${customerInfo}`);
      console.log(`   📦 Items: ${itemSummary}`);
      console.log(`   🔢 Count: ${group.count} identical orders`);
      console.log(`   💰 Total value: $${totalValue.toFixed(2)}`);
      console.log(`   🆔 Order IDs: ${group.orders.map(o => o.id).slice(0, 8).join(', ')}${group.count > 8 ? '...' : ''}`);
    }

    if (duplicateGroups.length > 15) {
      console.log(`\n... and ${duplicateGroups.length - 15} more groups`);
    }

    // Deletion process
    if (DRY_RUN) {
      console.log('\n🔧 DRY RUN MODE - No data will be deleted');
      console.log('💡 Set DRY_RUN = false in the script to actually delete the data');
      return;
    }

    console.log('\n⚠️  WARNING: About to delete duplicate orders!');
    console.log('🔄 Press Ctrl+C to cancel, or wait 10 seconds to continue...');
    await sleep(10000);

    console.log('\n🗑️  Starting deletion...');
    let deletedCount = 0;
    let failedCount = 0;

    for (const group of duplicateGroups) {
      console.log(`\n🔄 Deleting ${group.count} orders from group...`);
      
      for (const order of group.orders) {
        try {
          // Delete order items first
          await prisma.orderItem.deleteMany({
            where: { orderId: order.id }
          });

          // Delete the order
          await prisma.order.delete({
            where: { id: order.id }
          });

          deletedCount++;
          
          if (deletedCount % 10 === 0) {
            console.log(`✅ Deleted ${deletedCount} orders so far...`);
          }

          // Small delay to avoid overwhelming the database
          await sleep(50);

        } catch (error) {
          failedCount++;
          console.log(`❌ Failed to delete order #${order.id}: ${error.message}`);
        }
      }
    }

    console.log(`\n🎉 Cleanup complete!`);
    console.log(`   ✅ Successfully deleted: ${deletedCount} orders`);
    console.log(`   ❌ Failed: ${failedCount} orders`);

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    await findAndCleanDuplicateOrders();
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
