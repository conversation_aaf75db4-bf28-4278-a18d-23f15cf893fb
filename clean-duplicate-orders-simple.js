const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['error'],
  errorFormat: 'minimal'
});

// Configuration
const DUPLICATE_THRESHOLD = 5; // Delete if same order pattern appears 5+ times in same day
const DRY_RUN = true; // Set to false to actually delete data

// Sleep function for rate limiting
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to format date to YYYY-MM-DD for comparison
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

async function findAndCleanDuplicateOrders() {
  console.log('🔍 Finding duplicate orders (same customer, same day, same products)...');
  console.log(`📊 Threshold: ${DUPLICATE_THRESHOLD}+ identical orders`);
  console.log(`🔧 Mode: ${DRY_RUN ? 'DRY RUN (no deletions)' : 'LIVE MODE (will delete data)'}`);
  
  try {
    // Get all orders with their items, grouped by date and customer
    console.log('📖 Fetching orders...');
    
    const orders = await prisma.order.findMany({
      include: {
        items: {
          select: {
            itemName: true,
            quantity: true,
            productId: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        dateCreated: 'desc'
      }
    });

    console.log(`📊 Found ${orders.length} total orders`);

    // Group orders by: date + customer + product combination
    const orderGroups = new Map();
    
    for (const order of orders) {
      const date = formatDate(order.dateCreated);
      const customerId = order.customerId || `guest_${order.email}`;
      
      // Create a signature for the items in this order
      const itemSignature = order.items
        .map(item => `${item.itemName || item.productId || 'unknown'}_${item.quantity}`)
        .sort()
        .join('|');
      
      const groupKey = `${date}_${customerId}_${itemSignature}`;
      
      if (!orderGroups.has(groupKey)) {
        orderGroups.set(groupKey, []);
      }
      
      orderGroups.get(groupKey).push(order);
    }

    console.log(`📊 Found ${orderGroups.size} unique order patterns`);

    // Find groups with 5+ identical orders
    const duplicateGroups = [];
    let totalDuplicateOrders = 0;
    
    for (const [groupKey, groupOrders] of orderGroups) {
      if (groupOrders.length >= DUPLICATE_THRESHOLD) {
        duplicateGroups.push({
          key: groupKey,
          orders: groupOrders,
          count: groupOrders.length
        });
        totalDuplicateOrders += groupOrders.length;
      }
    }

    console.log(`\n🚨 Found ${duplicateGroups.length} groups with ${DUPLICATE_THRESHOLD}+ identical orders`);
    console.log(`📊 Total duplicate orders: ${totalDuplicateOrders}`);

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate orders found above threshold!');
      return;
    }

    // Sort by count (highest first) to show most problematic groups
    duplicateGroups.sort((a, b) => b.count - a.count);

    // Display details
    console.log('\n📋 Duplicate order groups:');
    for (let i = 0; i < Math.min(duplicateGroups.length, 15); i++) {
      const group = duplicateGroups[i];
      const firstOrder = group.orders[0];
      const date = formatDate(firstOrder.dateCreated);
      
      const customerInfo = firstOrder.customer 
        ? `${firstOrder.customer.name} (${firstOrder.customer.email})`
        : `Guest: ${firstOrder.email}`;
      
      const itemSummary = firstOrder.items
        .map(item => `${item.itemName || 'Product'} (qty: ${item.quantity})`)
        .join(', ');

      const totalValue = group.orders.reduce((sum, order) => sum + parseFloat(order.totalAmount), 0);

      console.log(`\n${i + 1}. 📅 ${date} | 👤 ${customerInfo}`);
      console.log(`   📦 Items: ${itemSummary}`);
      console.log(`   🔢 Count: ${group.count} identical orders`);
      console.log(`   💰 Total value: $${totalValue.toFixed(2)}`);
      console.log(`   🆔 Order IDs: ${group.orders.map(o => o.id).slice(0, 8).join(', ')}${group.count > 8 ? '...' : ''}`);
    }

    if (duplicateGroups.length > 15) {
      console.log(`\n... and ${duplicateGroups.length - 15} more groups`);
    }

    // Deletion process
    if (DRY_RUN) {
      console.log('\n🔧 DRY RUN MODE - No data will be deleted');
      console.log('💡 Set DRY_RUN = false in the script to actually delete the data');
      return;
    }

    console.log('\n⚠️  WARNING: About to delete duplicate orders!');
    console.log('🔄 Press Ctrl+C to cancel, or wait 10 seconds to continue...');
    await sleep(10000);

    console.log('\n🗑️  Starting deletion...');
    let deletedCount = 0;
    let failedCount = 0;

    for (const group of duplicateGroups) {
      console.log(`\n🔄 Deleting ${group.count} orders from group...`);
      
      for (const order of group.orders) {
        try {
          // Delete order items first
          await prisma.orderItem.deleteMany({
            where: { orderId: order.id }
          });

          // Delete the order
          await prisma.order.delete({
            where: { id: order.id }
          });

          deletedCount++;
          
          if (deletedCount % 10 === 0) {
            console.log(`✅ Deleted ${deletedCount} orders so far...`);
          }

          // Small delay to avoid overwhelming the database
          await sleep(50);

        } catch (error) {
          failedCount++;
          console.log(`❌ Failed to delete order #${order.id}: ${error.message}`);
        }
      }
    }

    console.log(`\n🎉 Cleanup complete!`);
    console.log(`   ✅ Successfully deleted: ${deletedCount} orders`);
    console.log(`   ❌ Failed: ${failedCount} orders`);

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    await findAndCleanDuplicateOrders();
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
