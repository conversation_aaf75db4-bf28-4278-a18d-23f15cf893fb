import { FilterSummary } from './product-filters.dto';
import { AccessLevel } from '@prisma/client';

export class PaginationDto {
  total: number;
  page: number;
  limit: number;
  totalPages?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

export class ProductDto {
  id: number;
  sku: string;
  name: string;
  slug: string;
  shortDescription?: string;
  imageUrl: string | null;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  stockQuantity?: number;
  stockStatus?: string;
  productType?: string;
  discountPercent?: number;
  isOnSale?: boolean;
  created_at: Date;
  updated_at?: Date;
  access: AccessLevel; // Visibility level: PUBLIC, PROTECTED (PRIVATE products are not returned)
  tags?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  categories?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
}

export class CategoryInfoDto {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

export class CategoryDetailDto {
  id: number;
  name: string;
  slug: string;
  description?: string;
  imageUrl?: string;
}

export class PaginatedProductsDto {
  pagination: PaginationDto;
  data: ProductDto[];
  filters?: FilterSummary;
  appliedFilters?: any;
}

export class EnhancedPaginatedProductsDto extends PaginatedProductsDto {
  categories: CategoryInfoDto[];
  category?: CategoryDetailDto;
}


