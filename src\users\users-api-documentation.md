# Users API Documentation

This API provides endpoints for managing and retrieving user information with pagination and search capabilities.

## Authentication & Authorization

All endpoints require:
- **Authentication**: Valid JWT token
- **Authorization**: Admin or Super Admin role

## Endpoints

### 1. Get All Users (Paginated)

Retrieve a paginated list of all users with optional search functionality.

#### Endpoint
```
GET /users
```

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Page number (minimum: 1) |
| `limit` | number | No | 30 | Number of users per page (minimum: 1, maximum: 100) |
| `search` | string | No | - | Search term to filter users by name, email, firstName, or lastName |

#### Request Examples

```bash
# Get first page with default limit (30 users)
GET /users

# Get specific page with custom limit
GET /users?page=2&limit=50

# Search users by email
GET /users?search=<EMAIL>

# Search users by name
GET /users?search=john

# Search with pagination
GET /users?search=admin&page=1&limit=10
```

#### Response Format

```json
{
  "pagination": {
    "total": 19946,
    "page": 1,
    "limit": 30,
    "totalPages": 665,
    "hasNext": true,
    "hasPrev": false
  },
  "data": [
    {
      "id": 1,
      "name": "admin",
      "email": "<EMAIL>",
      "firstName": "Admin",
      "lastName": "User",
      "isAdmin": true,
      "isSuperAdmin": true,
      "isVerified": true,
      "isBanned": false,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastLogin": "2024-01-15T10:30:00.000Z",
      "points": 0,
      "city": "New York",
      "country": "USA",
      "companyName": "Example Corp"
    },
    {
      "id": 2,
      "name": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "isAdmin": false,
      "isSuperAdmin": false,
      "isVerified": true,
      "isBanned": false,
      "createdAt": "2024-01-02T00:00:00.000Z",
      "lastLogin": null,
      "points": 150,
      "city": "Los Angeles",
      "country": "USA",
      "companyName": null
    }
  ],
  "searchTerm": "john"
}
```

#### Response Fields

**Pagination Object:**
- `total`: Total number of users matching the search criteria
- `page`: Current page number
- `limit`: Number of users per page
- `totalPages`: Total number of pages available
- `hasNext`: Boolean indicating if there's a next page
- `hasPrev`: Boolean indicating if there's a previous page

**User Object:**
- `id`: Unique user identifier
- `name`: Username/login name
- `email`: User's email address
- `firstName`: User's first name (nullable)
- `lastName`: User's last name (nullable)
- `isAdmin`: Boolean indicating admin status
- `isSuperAdmin`: Boolean indicating super admin status
- `isVerified`: Boolean indicating if email is verified
- `isBanned`: Boolean indicating if user is banned
- `createdAt`: Account creation timestamp
- `lastLogin`: Last login timestamp (nullable)
- `points`: User's reward points
- `city`: User's city (nullable)
- `country`: User's country (nullable)
- `companyName`: User's company name (nullable)

**Additional Fields:**
- `searchTerm`: The search term that was applied (only present if search was used)

### 2. Get User by ID

Retrieve detailed information about a specific user.

#### Endpoint
```
GET /users/:id
```

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | number | Yes | User ID |

#### Request Example

```bash
GET /users/123
```

#### Response Format

```json
{
  "id": 123,
  "name": "johndoe",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "isAdmin": false,
  "isSuperAdmin": false,
  "isVerified": true,
  "isBanned": false,
  "createdAt": "2024-01-02T00:00:00.000Z",
  "lastLogin": "2024-01-15T14:30:00.000Z",
  "points": 150,
  "city": "Los Angeles",
  "country": "USA",
  "companyName": "Tech Solutions Inc"
}
```

## Search Functionality

The search parameter supports searching across multiple fields:

1. **Username** (`name`) - Case-insensitive partial matching
2. **Email** (`email`) - Case-insensitive partial matching
3. **First Name** (`firstName`) - Case-insensitive partial matching
4. **Last Name** (`lastName`) - Case-insensitive partial matching
5. **Full Name** - Searches across combined first and last name

### Search Examples

```bash
# Search by email domain
GET /users?search=@gmail.com

# Search by first name
GET /users?search=john

# Search by last name
GET /users?search=smith

# Search by full name
GET /users?search=john doe

# Search by username
GET /users?search=admin
```

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": [
    "Page must be at least 1",
    "Limit must not exceed 100"
  ],
  "error": "Bad Request"
}
```

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

### 403 Forbidden
```json
{
  "statusCode": 403,
  "message": "Insufficient permissions. Admin role required.",
  "error": "Forbidden"
}
```

### 404 Not Found (for single user endpoint)
```json
{
  "statusCode": 404,
  "message": "User with ID 999 not found",
  "error": "Not Found"
}
```

## Usage Examples

### Basic Pagination
```bash
# First page
GET /users?page=1&limit=20

# Next page
GET /users?page=2&limit=20

# Large page size
GET /users?page=1&limit=100
```

### Search Operations
```bash
# Find all admins
GET /users?search=admin

# Find users from specific company
GET /users?search=microsoft

# Find users by email pattern
GET /users?search=@company.com

# Search with pagination
GET /users?search=john&page=1&limit=10
```

### Combined Operations
```bash
# Search and paginate
GET /users?search=verified&page=2&limit=25

# Find specific user types
GET /users?search=super&limit=50
```

## Rate Limiting

- Standard rate limiting applies (100 requests per minute per IP)
- Admin endpoints may have additional restrictions

## Notes

- All timestamps are in ISO 8601 format (UTC)
- Search is case-insensitive
- Pagination starts from page 1
- Maximum limit is 100 users per page
- Default limit is 30 users per page
- Users with placeholder passwords (from CSV import) will show `isVerified: false`
