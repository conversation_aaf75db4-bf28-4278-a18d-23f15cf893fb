-- Setup script for search functionality
-- This script ensures that the search_vector column and indexes are properly set up
-- Run this after migrating to a new database if search functionality is not working

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Ensure search_vector column exists and is properly configured
DO $$
BEGIN
    -- Check if search_vector column exists and is a generated column
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Product'
        AND column_name = 'search_vector'
        AND is_generated = 'ALWAYS'
    ) THEN
        RAISE NOTICE 'search_vector column already exists as generated column';
    ELSIF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Product'
        AND column_name = 'search_vector'
    ) THEN
        -- Column exists but is not generated, drop and recreate
        RAISE NOTICE 'Recreating search_vector as generated column';
        DROP INDEX IF EXISTS "Product_search_vector_idx";
        ALTER TABLE "Product" DROP COLUMN search_vector;
        ALTER TABLE "Product"
        ADD COLUMN search_vector tsvector GENERATED ALWAYS AS (
            setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
            setweight(to_tsvector('english', coalesce("shortDescription", '')), 'B') ||
            setweight(to_tsvector('english', coalesce(description, '')), 'C')
        ) STORED;
    ELSE
        -- Column doesn't exist, create it
        RAISE NOTICE 'Creating search_vector as generated column';
        ALTER TABLE "Product"
        ADD COLUMN search_vector tsvector GENERATED ALWAYS AS (
            setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
            setweight(to_tsvector('english', coalesce("shortDescription", '')), 'B') ||
            setweight(to_tsvector('english', coalesce(description, '')), 'C')
        ) STORED;
    END IF;
END
$$;

-- Ensure search indexes exist
DO $$
BEGIN
    -- Drop old indexes if they exist
    DROP INDEX IF EXISTS "product_search_vector_idx";
    DROP INDEX IF EXISTS "product_name_trgm_idx";
    
    -- Create new indexes with correct names if they don't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'Product_search_vector_idx'
    ) THEN
        CREATE INDEX "Product_search_vector_idx"
          ON "Product"
          USING GIN(search_vector);
        RAISE NOTICE 'Created Product_search_vector_idx';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'Product_name_idx'
    ) THEN
        CREATE INDEX "Product_name_idx"
          ON "Product"
          USING GIN (name gin_trgm_ops);
        RAISE NOTICE 'Created Product_name_idx';
    END IF;
      
    RAISE NOTICE 'Search setup completed successfully';
END
$$;
