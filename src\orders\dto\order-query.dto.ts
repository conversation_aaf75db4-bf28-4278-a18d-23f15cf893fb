import { IsOptional, IsString, <PERSON>N<PERSON><PERSON>, IsEnum, IsDateString, <PERSON>, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export enum OrderSortBy {
  DATE_CREATED_DESC = 'dateCreated_desc',
  DATE_CREATED_ASC = 'dateCreated_asc',
  TOTAL_AMOUNT_DESC = 'totalAmount_desc',
  TOTAL_AMOUNT_ASC = 'totalAmount_asc',
  STATUS = 'status',
  CUSTOMER_NAME = 'customerName',
}

export enum OrderStatusFilter {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

export class OrderQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 30;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(OrderSortBy)
  sortBy?: OrderSortBy = OrderSortBy.DATE_CREATED_DESC;

  @IsOptional()
  @IsEnum(OrderStatusFilter)
  status?: OrderStatusFilter;

  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @IsOptional()
  @IsDateString()
  dateTo?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  customerId?: number;
}
