# Shop API Filters Documentation

This document describes all available filters and sorting options for the shop API endpoints.

## Endpoints Supporting Filters

All the following endpoints now support comprehensive filtering:

- `GET /shop/products` - Get all products
- `GET /shop/categories/:catId/products` - Get products by category
- `GET /shop/wholesale` - Get all products in the Wholesale category
- `GET /shop/:mainCategorySlug` - Get products by main category
- `GET /shop/:mainCategorySlug/:categorySlug` - Get products by main and sub category

## Filter Information Endpoints

These endpoints provide filter metadata for building dynamic filter UIs:

- `GET /shop/products/filters` - Get filter summary for all products
- `GET /shop/categories/:catId/filters` - Get filter summary for specific category
- `GET /shop/wholesale/filters` - Get filter summary for wholesale category
- `GET /shop/:mainCategorySlug/filters` - Get filter summary for main category

## Available Filters

### Pagination
- `page` (number, default: 1) - Page number
- `limit` (number, default: 20, max: 100) - Items per page

### Price Filters
- `minPrice` (number) - Minimum price filter
- `maxPrice` (number) - Maximum price filter
- `priceRange` (string) - Predefined price ranges: "0-50", "50-100", "100-200", "200+"
- `onSale` (boolean) - Filter products on sale only
- `minDiscountPercent` (number, 0-100) - Minimum discount percentage

### Stock Filters
- `inStock` (boolean) - Filter by stock availability
- `stockStatus` (enum) - Filter by specific stock status: "IN_STOCK", "OUT_OF_STOCK", "ON_BACKORDER"
- `minStock` (number) - Minimum stock quantity
- `maxStock` (number) - Maximum stock quantity

### Product Type Filter
- `productType` (enum) - Filter by product type: "SIMPLE", "VARIABLE", "GROUPED", "EXTERNAL"

### Category Filters
- `categoryIds` (number[]) - Filter by category IDs
- `categorySlugs` (string[]) - Filter by category slugs

### Tag Filters
- `tagIds` (number[]) - Filter by tag IDs
- `tagNames` (string[]) - Filter by tag names

### Date Filters
- `createdAfter` (date) - Products created after this date
- `createdBefore` (date) - Products created before this date

### Search
- `search` (string) - Search in product name, description, and SKU
- `sku` (string) - Filter by SKU (partial match)

### Sorting Options
- `sortBy` (enum) - Sort products by:
  - `price_asc` - Price low to high
  - `price_desc` - Price high to low
  - `newest` - Newest first (default)
  - `oldest` - Oldest first
  - `name_asc` - Name A to Z
  - `name_desc` - Name Z to A
  - `popularity` - By popularity
  - `rating` - By rating
  - `stock_status` - By stock status

### Other Filters
- `featured` (boolean) - Filter featured products (future implementation)

## Example Requests

### 1. Get all products with basic pagination
```
GET /shop/products?page=1&limit=20
```

### 2. Filter products by price range
```
GET /shop/products?minPrice=50&maxPrice=200&sortBy=price_asc
```

### 3. Get products on sale, sorted by discount
```
GET /shop/products?onSale=true&sortBy=price_desc
```

### 4. Filter by stock status and sort by newest
```
GET /shop/products?inStock=true&sortBy=newest
```

### 5. Search products with price filter
```
GET /shop/products?search=laptop&minPrice=500&maxPrice=2000
```

### 6. Filter by multiple categories
```
GET /shop/products?categoryIds=1,2,3&sortBy=popularity
```

### 7. Filter by tags and date range
```
GET /shop/products?tagNames=electronics,gaming&createdAfter=2024-01-01
```

### 8. Complex filter example
```
GET /shop/products?minPrice=100&maxPrice=500&onSale=true&inStock=true&productType=SIMPLE&sortBy=price_asc&page=1&limit=12
```

### 9. Filter products in specific category with price range
```
GET /shop/categories/5/products?minPrice=50&maxPrice=300&sortBy=price_desc
```

### 10. Filter by predefined price ranges
```
GET /shop/products?priceRange=100-200&sortBy=newest
```

### 11. Get filter information for building UI
```
GET /shop/products/filters
```

### 12. Get category-specific filter information
```
GET /shop/categories/5/filters
```

### 13. Get wholesale products with filters
```
GET /shop/wholesale?minPrice=100&onSale=true&sortBy=price_desc&page=1&limit=20
```

### 14. Search wholesale products
```
GET /shop/wholesale?search=bulk&inStock=true&sortBy=newest
```

### 15. Get wholesale filter information
```
GET /shop/wholesale/filters
```

## Wholesale Endpoint

The wholesale endpoint provides access to all products in the "Wholesale" category with full filtering capabilities.

### Endpoint
```
GET /shop/wholesale
```

### Features
- **Automatic Category Filtering**: Only returns products from the "Wholesale" category
- **Full Filter Support**: Supports all available filters (price, stock, search, etc.)
- **Category Information**: Returns wholesale category details (name, description, image)
- **Error Handling**: Returns 404 if "Wholesale" category doesn't exist

### Example Request
```
GET /shop/wholesale?minPrice=50&maxPrice=500&onSale=true&sortBy=price_asc&page=1&limit=12
```

### Example Response
```json
{
  "pagination": {
    "total": 45,
    "page": 1,
    "limit": 12,
    "totalPages": 4,
    "hasNext": true,
    "hasPrev": false
  },
  "data": [
    {
      "id": 101,
      "sku": "BULK-001",
      "name": "Wholesale Product Bundle",
      "slug": "wholesale-product-bundle",
      "shortDescription": "Bulk quantity product for wholesale customers",
      "imageUrl": "https://example.com/wholesale-product.jpg",
      "price": 299.99,
      "salePrice": 249.99,
      "inStock": true,
      "stockQuantity": 100,
      "stockStatus": "IN_STOCK",
      "productType": "GROUPED",
      "discountPercent": 17,
      "isOnSale": true,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-20T14:45:00Z",
      "tags": [
        {
          "id": 5,
          "name": "Bulk",
          "slug": "bulk"
        },
        {
          "id": 12,
          "name": "Wholesale",
          "slug": "wholesale"
        }
      ],
      "categories": [
        {
          "id": 8,
          "name": "Wholesale",
          "slug": "wholesale"
        }
      ]
    }
  ],
  "categories": [],
  "appliedFilters": {
    "minPrice": 50,
    "maxPrice": 500,
    "onSale": true,
    "sortBy": "price_asc",
    "page": 1,
    "limit": 12
  },
  "category": {
    "id": 8,
    "name": "Wholesale",
    "slug": "wholesale",
    "description": "Bulk products for wholesale customers with special pricing",
    "imageUrl": "https://example.com/wholesale-category.jpg"
  }
}
```

### Wholesale Filters Endpoint
```
GET /shop/wholesale/filters
```

Returns filter metadata specifically for wholesale products:

```json
{
  "totalProducts": 45,
  "priceRange": {
    "min": 49.99,
    "max": 1999.99
  },
  "availableCategories": [
    {
      "id": 8,
      "name": "Wholesale",
      "slug": "wholesale",
      "count": 45
    }
  ],
  "availableTags": [
    {
      "id": 5,
      "name": "Bulk",
      "slug": "bulk",
      "count": 30
    },
    {
      "id": 12,
      "name": "Wholesale",
      "slug": "wholesale",
      "count": 45
    },
    {
      "id": 18,
      "name": "Commercial",
      "slug": "commercial",
      "count": 15
    }
  ],
  "stockStatusCounts": {
    "inStock": 40,
    "outOfStock": 3,
    "onBackorder": 2
  },
  "productTypeCounts": {
    "simple": 25,
    "grouped": 20
  },
  "onSaleCount": 12
}
```

### Error Response
If the "Wholesale" category doesn't exist:

```json
{
  "statusCode": 404,
  "message": "Wholesale category not found",
  "error": "Not Found"
}
```

## Response Format

All filtered endpoints return enhanced product information:

```json
{
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  },
  "data": [
    {
      "id": 1,
      "sku": "PROD-001",
      "name": "Product Name",
      "slug": "product-name",
      "shortDescription": "Product description",
      "imageUrl": "https://example.com/image.jpg",
      "price": 99.99,
      "salePrice": 79.99,
      "inStock": true,
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "productType": "SIMPLE",
      "discountPercent": 20,
      "isOnSale": true,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-20T14:45:00Z",
      "tags": [
        {
          "id": 1,
          "name": "Electronics",
          "slug": "electronics"
        }
      ],
      "categories": [
        {
          "id": 5,
          "name": "Laptops",
          "slug": "laptops"
        }
      ]
    }
  ],
  "appliedFilters": {
    "minPrice": 50,
    "maxPrice": 300,
    "onSale": true,
    "sortBy": "price_asc"
  }
}
```

## Filter Summary Response

The filter endpoints (`/products/filters`, `/categories/:id/filters`, etc.) return metadata for building filter UIs:

```json
{
  "totalProducts": 150,
  "priceRange": {
    "min": 9.99,
    "max": 2999.99
  },
  "availableCategories": [
    {
      "id": 1,
      "name": "Electronics",
      "slug": "electronics",
      "count": 45
    },
    {
      "id": 2,
      "name": "Clothing",
      "slug": "clothing",
      "count": 32
    }
  ],
  "availableTags": [
    {
      "id": 1,
      "name": "Gaming",
      "slug": "gaming",
      "count": 23
    },
    {
      "id": 2,
      "name": "Portable",
      "slug": "portable",
      "count": 18
    }
  ],
  "stockStatusCounts": {
    "inStock": 120,
    "outOfStock": 25,
    "onBackorder": 5
  },
  "productTypeCounts": {
    "simple": 130,
    "grouped": 20
  },
  "onSaleCount": 45
}
```

## Filter Combinations

Filters can be combined for powerful product discovery:

1. **Price + Stock**: `minPrice=100&inStock=true`
2. **Category + Sale**: `categoryIds=1,2&onSale=true`
3. **Search + Price + Sort**: `search=gaming&minPrice=200&sortBy=price_desc`
4. **Date + Type**: `createdAfter=2024-01-01&productType=GROUPED`
5. **Tags + Stock + Price**: `tagNames=electronics&inStock=true&maxPrice=500`

## Example Response for Enhanced Category Endpoint

For endpoints that return categories (main category and category-specific endpoints):

```json
{
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 12,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  },
  "data": [
    // ... product data as shown above
  ],
  "categories": [
    {
      "id": 1,
      "name": "Laptops",
      "slug": "laptops",
      "imageUrl": "https://example.com/category-laptops.jpg",
      "count": 45
    },
    {
      "id": 2,
      "name": "Desktops",
      "slug": "desktops",
      "imageUrl": "https://example.com/category-desktops.jpg",
      "count": 23
    }
  ],
  "appliedFilters": {
    "minPrice": 500,
    "sortBy": "price_asc"
  }
}
```

## Advanced Filter Examples

### Filter by discount percentage
```
GET /shop/products?minDiscountPercent=25&sortBy=price_desc
```
Returns products with at least 25% discount, sorted by price high to low.

### Filter by stock quantity range
```
GET /shop/products?minStock=10&maxStock=100&sortBy=stock_status
```
Returns products with stock between 10-100 units.

### Multiple tag filter
```
GET /shop/products?tagNames=electronics,gaming,portable&sortBy=popularity
```
Returns products that have any of the specified tags.

### Date range with category filter
```
GET /shop/categories/5/products?createdAfter=2024-01-01&createdBefore=2024-12-31&sortBy=newest
```
Returns products in category 5 created in 2024.

## Error Responses

### Invalid filter values
```json
{
  "statusCode": 400,
  "message": [
    "minPrice must be a positive number",
    "sortBy must be one of the following values: price_asc, price_desc, newest, oldest, name_asc, name_desc, popularity, rating, stock_status"
  ],
  "error": "Bad Request"
}
```

### Category not found
```json
{
  "statusCode": 404,
  "message": "Category with ID 999 not found",
  "error": "Not Found"
}
```

## Performance Tips

1. Use pagination (`limit`) to control response size
2. Combine filters to reduce dataset before sorting
3. Use specific category filters instead of searching all products
4. Price range filters are more efficient than open-ended price filters

## Notes

- All price filters work with both regular price and sale price
- Boolean filters accept "true"/"false" strings or actual boolean values
- Array filters accept comma-separated values or multiple query parameters
- Date filters accept ISO 8601 format dates
- Search is case-insensitive and searches across name, description, and SKU
- Private products are automatically excluded from all results
- Discount percentage is automatically calculated when products have sale prices
- Stock status is automatically determined based on stock quantity and business rules
