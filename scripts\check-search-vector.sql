-- Check the current state of the search_vector column
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    is_generated,
    generation_expression
FROM information_schema.columns 
WHERE table_name = 'Product' 
AND column_name = 'search_vector';

-- Check existing indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'Product' 
AND (indexname LIKE '%search%' OR indexname LIKE '%name%');
