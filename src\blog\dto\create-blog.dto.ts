import { IsNotEmpty, IsString, IsOptional, IsArray, IsBoolean, IsInt, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';

export class BlogContentBlockDto {
  @IsNotEmpty()
  @IsString()
  type: string; // 'text', 'image', 'heading', 'list', 'quote'

  @IsNotEmpty()
  @IsString()
  content: string;

  @IsOptional()
  @IsString()
  imageUrl?: string;

  @IsOptional()
  @IsString()
  imageAlt?: string;

  @IsOptional()
  @IsInt()
  level?: number; // For headings

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  listItems?: string[];
}

export class BlogAuthorDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  avatar?: string;

  @IsOptional()
  @IsString()
  bio?: string;
}

export class BlogSeoDto {
  @IsOptional()
  @IsString()
  metaTitle?: string;

  @IsOptional()
  @IsString()
  metaDescription?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];
}

export class CreateBlogDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  slug: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsNotEmpty()
  @IsArray()
  @Type(() => BlogContentBlockDto)
  content: BlogContentBlockDto[];

  @IsOptional()
  @IsString()
  blogImage?: string;

  @IsNotEmpty()
  @Type(() => BlogAuthorDto)
  author: BlogAuthorDto;

  @IsOptional()
  @IsString()
  excerpt?: string;

  @IsOptional()
  @IsInt()
  @IsPositive()
  readTime?: number;

  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @IsOptional()
  @IsBoolean()
  published?: boolean;

  @IsOptional()
  @Type(() => BlogSeoDto)
  seo?: BlogSeoDto;
}
