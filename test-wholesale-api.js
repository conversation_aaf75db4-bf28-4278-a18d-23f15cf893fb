// Test script for Wholesale API endpoint
const baseUrl = 'http://localhost:3000';

async function testWholesaleAPI(endpoint, description) {
  console.log(`\n🧪 Testing: ${description}`);
  console.log(`📡 URL: ${baseUrl}${endpoint}`);
  
  try {
    const response = await fetch(`${baseUrl}${endpoint}`);
    const data = await response.json();
    
    console.log(`✅ Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log(`📊 Response Structure:`, {
        hasPagination: !!data.pagination,
        hasData: !!data.data,
        hasCategory: !!data.category,
        hasAppliedFilters: !!data.appliedFilters,
        dataCount: data.data?.length || 0,
        totalProducts: data.pagination?.total || 0
      });
      
      if (data.category) {
        console.log(`🏷️ Category Info:`, {
          id: data.category.id,
          name: data.category.name,
          slug: data.category.slug,
          hasDescription: !!data.category.description,
          hasImage: !!data.category.imageUrl
        });
      }
      
      if (data.appliedFilters) {
        console.log(`🔍 Applied Filters:`, data.appliedFilters);
      }
      
      if (data.data && data.data.length > 0) {
        console.log(`📦 Sample Product:`, {
          id: data.data[0].id,
          name: data.data[0].name,
          price: data.data[0].price,
          sku: data.data[0].sku,
          categoriesCount: data.data[0].categories?.length || 0,
          tagsCount: data.data[0].tags?.length || 0
        });
      }
    } else {
      console.log(`❌ Error Response:`, data);
    }
    
  } catch (error) {
    console.log(`❌ Request Error:`, error.message);
  }
}

async function runWholesaleTests() {
  console.log('🚀 Starting Wholesale API Tests...');
  
  // Test basic wholesale endpoint
  await testWholesaleAPI('/api/shop/wholesale', 'Get wholesale products (default)');
  
  // Test with pagination
  await testWholesaleAPI('/api/shop/wholesale?page=1&limit=5', 'Get wholesale products (page 1, limit 5)');
  
  // Test with price filters
  await testWholesaleAPI('/api/shop/wholesale?minPrice=50&maxPrice=500', 'Get wholesale products (price filter)');
  
  // Test with search
  await testWholesaleAPI('/api/shop/wholesale?search=bulk', 'Search wholesale products for "bulk"');
  
  // Test with sorting
  await testWholesaleAPI('/api/shop/wholesale?sortBy=price_asc&limit=3', 'Get wholesale products (sorted by price)');
  
  // Test with stock filter
  await testWholesaleAPI('/api/shop/wholesale?inStock=true&limit=3', 'Get wholesale products (in stock only)');
  
  // Test with sale filter
  await testWholesaleAPI('/api/shop/wholesale?onSale=true', 'Get wholesale products (on sale only)');
  
  // Test complex filter combination
  await testWholesaleAPI('/api/shop/wholesale?minPrice=100&inStock=true&sortBy=price_desc&page=1&limit=2', 'Complex wholesale filter');
  
  // Test wholesale filters endpoint
  await testWholesaleAPI('/api/shop/wholesale/filters', 'Get wholesale filter summary');
  
  console.log('\n✅ All wholesale tests completed!');
}

// Run the tests
runWholesaleTests().catch(console.error);
