import { 
  <PERSON>, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  ParseIntPipe, 
  Query,
  ParseBoolPipe,
  DefaultValuePipe
} from '@nestjs/common';
import { BlogService } from './blog.service';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import { BlogResponseDto, BlogListResponseDto, PaginatedBlogsResponseDto } from './dto/blog-response.dto';

@Controller('blog')
export class BlogController {
  constructor(private readonly blogService: BlogService) {}

  @Post()
  create(@Body() createBlogDto: CreateBlogDto): Promise<BlogResponseDto> {
    return this.blogService.create(createBlogDto);
  }

  @Get()
  findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('published') published?: string,
    @Query('featured') featured?: string,
    @Query('tags') tags?: string,
  ): Promise<PaginatedBlogsResponseDto> {
    const publishedBool = published === 'true' ? true : published === 'false' ? false : undefined;
    const featuredBool = featured === 'true' ? true : featured === 'false' ? false : undefined;
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()) : undefined;
    
    return this.blogService.findAll(page, limit, publishedBool, featuredBool, tagsArray);
  }

  @Get('featured')
  getFeaturedBlogs(): Promise<BlogListResponseDto[]> {
    return this.blogService.getFeaturedBlogs();
  }

  @Get('tags')
  getAllTags(): Promise<string[]> {
    return this.blogService.getAllTags();
  }

  @Get('slug/:slug')
  findBySlug(@Param('slug') slug: string): Promise<BlogResponseDto> {
    return this.blogService.findBySlug(slug);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<BlogResponseDto> {
    return this.blogService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateBlogDto: UpdateBlogDto,
  ): Promise<BlogResponseDto> {
    return this.blogService.update(id, updateBlogDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<BlogResponseDto> {
    return this.blogService.remove(id);
  }
}
