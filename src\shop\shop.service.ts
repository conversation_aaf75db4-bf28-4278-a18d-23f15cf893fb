import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { MainCategoryDto } from './dto/main-category.dto';
import { SubcategoriesDto } from './dto/subcategories.dto';
import { PaginatedProductsDto, EnhancedPaginatedProductsDto } from './dto/paginated-products.dto';
import { ProductDetailsDto } from './dto/product-details.dto';
import { ProductFiltersDto, SortBy, StockStatus, ProductType, FilterSummary, PriceRange } from './dto/product-filters.dto';
import { AccessLevel, Prisma } from '@prisma/client';

@Injectable()
export class ShopService {
  constructor(private prisma: PrismaService) {}

  async getMainCategories(): Promise<{ data: MainCategoryDto[] }> {
    // Get all main categories
    const mainCategories = await this.prisma.mainCategory.findMany();

    // For each main category, count the products in its subcategories
    const mainCategoriesWithCount = await Promise.all(
      mainCategories.map(async (mainCategory) => {
        // Get all subcategories for this main category
        const subcategories = await this.prisma.category.findMany({
          where: { mainCategoryId: mainCategory.id },
          select: { id: true },
        });

        // Get the count of products for all subcategories
        const subcategoryIds = subcategories.map((cat) => cat.id);

        let count = 0;
        if (subcategoryIds.length > 0) {
          count = await this.prisma.productCategories.count({
            where: {
              categoryId: {
                in: subcategoryIds,
              },
            },
          });
        }

        return {
          id: mainCategory.id,
          name: mainCategory.name,
          slug: mainCategory.slug,
          imageUrl: mainCategory.imageUrl,
          count,
        };
      })
    );

    return { data: mainCategoriesWithCount };
  }

  async getSubcategories(mainId: number): Promise<SubcategoriesDto> {
    // Get the main category
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { id: mainId },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with ID ${mainId} not found`);
    }

    // Get all subcategories for this main category
    const subcategories = await this.prisma.category.findMany({
      where: { mainCategoryId: mainId },
    });

    // For each subcategory, count the products
    const subcategoriesWithCount = await Promise.all(
      subcategories.map(async (category) => {
        const count = await this.prisma.productCategories.count({
          where: { categoryId: category.id },
        });

        return {
          id: category.id,
          name: category.name,
          slug: category.slug,
          imageUrl: category.imageUrl,
          count,
          parentId: category.mainCategoryId,
        };
      })
    );

    return {
      mainCategory: {
        id: mainCategory.id,
        name: mainCategory.name,
        slug: mainCategory.slug,
      },
      subcategories: subcategoriesWithCount,
    };
  }

  async getProductsByCategory(
    categoryId: number,
    filters: ProductFiltersDto = {}
  ): Promise<PaginatedProductsDto> {
    // Check if category exists
    const category = await this.prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`);
    }

    const page = filters.page || 1;
    const limit = filters.limit || 20;

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause with category filter
    const where = this.buildProductWhereClause(filters, categoryId);

    // Build order by clause
    const orderBy = this.buildOrderByClause(filters.sortBy);

    // Get total count of products in this category
    const total = await this.prisma.product.count({ where });

    // Get products for this category with enhanced information
    const products = await this.prisma.product.findMany({
      where,
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        stockQuantity: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        shortDescription: true,
        access: true, // Include access level for visibility
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy,
      skip,
      take,
    });

    // Format the products
    const formattedProducts = products.map(product => this.formatProductWithFilters(product));

    return {
      pagination: this.calculatePagination(total, page, limit),
      data: formattedProducts,
      appliedFilters: { ...filters, categoryIds: [categoryId] },
    };
  }

  // Helper method to generate slug from name
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-'); // Replace multiple hyphens with a single one
  }

  // Helper method to build where clause for product filtering
  private buildProductWhereClause(filters: ProductFiltersDto, categoryId?: number, mainCategoryId?: number): any {
    const where: any = {
      access: {
        not: AccessLevel.PRIVATE
      }
    };

    // Category filters
    if (categoryId) {
      where.categories = {
        some: { id: categoryId }
      };
    } else if (mainCategoryId) {
      where.categories = {
        some: {
          mainCategoryId: mainCategoryId
        }
      };
    } else if (filters.categoryIds?.length) {
      where.categories = {
        some: {
          id: { in: filters.categoryIds }
        }
      };
    } else if (filters.categorySlugs?.length) {
      where.categories = {
        some: {
          slug: { in: filters.categorySlugs }
        }
      };
    }

    // Price filters
    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      where.OR = [
        // Check regular price
        {
          price: {
            ...(filters.minPrice !== undefined && { gte: filters.minPrice }),
            ...(filters.maxPrice !== undefined && { lte: filters.maxPrice })
          }
        },
        // Check sale price if it exists
        {
          AND: [
            { salePrice: { not: null } },
            {
              salePrice: {
                ...(filters.minPrice !== undefined && { gte: filters.minPrice }),
                ...(filters.maxPrice !== undefined && { lte: filters.maxPrice })
              }
            }
          ]
        }
      ];
    }

    // Handle price range presets
    if (filters.priceRange) {
      const range = this.parsePriceRange(filters.priceRange);
      if (range.min !== undefined || range.max !== undefined) {
        where.OR = [
          {
            price: {
              ...(range.min !== undefined && { gte: range.min }),
              ...(range.max !== undefined && { lte: range.max })
            }
          },
          {
            AND: [
              { salePrice: { not: null } },
              {
                salePrice: {
                  ...(range.min !== undefined && { gte: range.min }),
                  ...(range.max !== undefined && { lte: range.max })
                }
              }
            ]
          }
        ];
      }
    }

    // Sale filter
    if (filters.onSale === true) {
      where.salePrice = { not: null };
    } else if (filters.onSale === false) {
      where.salePrice = null;
    }

    // Stock filters
    if (filters.inStock === true) {
      where.stockStatus = 'IN_STOCK';
    } else if (filters.inStock === false) {
      where.stockStatus = { not: 'IN_STOCK' };
    }

    if (filters.stockStatus) {
      where.stockStatus = filters.stockStatus;
    }

    if (filters.minStock !== undefined) {
      where.stockQuantity = { gte: filters.minStock };
    }

    if (filters.maxStock !== undefined) {
      where.stockQuantity = {
        ...(where.stockQuantity || {}),
        lte: filters.maxStock
      };
    }

    // Product type filter
    if (filters.productType) {
      where.type = filters.productType;
    }

    // Tag filters
    if (filters.tagIds?.length) {
      where.tags = {
        some: {
          id: { in: filters.tagIds }
        }
      };
    } else if (filters.tagNames?.length) {
      where.tags = {
        some: {
          name: { in: filters.tagNames }
        }
      };
    }

    // Date filters
    if (filters.createdAfter) {
      where.createdAt = { gte: filters.createdAfter };
    }

    if (filters.createdBefore) {
      where.createdAt = {
        ...(where.createdAt || {}),
        lte: filters.createdBefore
      };
    }

    // SKU filter
    if (filters.sku) {
      where.sku = { contains: filters.sku, mode: 'insensitive' };
    }

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.trim().toLowerCase();
      where.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { description: { contains: searchTerm, mode: 'insensitive' } },
        { shortDescription: { contains: searchTerm, mode: 'insensitive' } },
        { sku: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    // Discount percentage filter
    if (filters.minDiscountPercent !== undefined) {
      where.AND = [
        ...(where.AND || []),
        { salePrice: { not: null } },
        {
          OR: [
            {
              AND: [
                { price: { gt: 0 } },
                { salePrice: { lt: { multiply: ['price', (100 - filters.minDiscountPercent) / 100] } } }
              ]
            }
          ]
        }
      ];
    }

    return where;
  }

  // Helper method to build order by clause
  private buildOrderByClause(sortBy: SortBy = SortBy.NEWEST_FIRST): any {
    switch (sortBy) {
      case SortBy.PRICE_LOW_TO_HIGH:
        return [
          { salePrice: { sort: 'asc', nulls: 'last' } },
          { price: 'asc' }
        ];
      case SortBy.PRICE_HIGH_TO_LOW:
        return [
          { salePrice: { sort: 'desc', nulls: 'last' } },
          { price: 'desc' }
        ];
      case SortBy.NEWEST_FIRST:
        return { createdAt: 'desc' };
      case SortBy.OLDEST_FIRST:
        return { createdAt: 'asc' };
      case SortBy.NAME_A_TO_Z:
        return { name: 'asc' };
      case SortBy.NAME_Z_TO_A:
        return { name: 'desc' };
      case SortBy.STOCK_STATUS:
        return [
          { stockStatus: 'desc' }, // IN_STOCK first
          { stockQuantity: 'desc' }
        ];
      case SortBy.POPULARITY:
        // For now, sort by creation date as a proxy for popularity
        // In the future, this could be based on view count, sales, etc.
        return { createdAt: 'desc' };
      case SortBy.RATING:
        // For now, sort by creation date as a proxy for rating
        // In the future, this could be based on actual ratings
        return { createdAt: 'desc' };
      default:
        return { createdAt: 'desc' };
    }
  }

  // Helper method to parse price range strings
  private parsePriceRange(priceRange: string): PriceRange {
    const range: PriceRange = {};

    if (priceRange.includes('-')) {
      const [min, max] = priceRange.split('-').map(p => p.trim());
      if (min && !isNaN(Number(min))) {
        range.min = Number(min);
      }
      if (max && !isNaN(Number(max))) {
        range.max = Number(max);
      }
    } else if (priceRange.endsWith('+')) {
      const min = priceRange.replace('+', '').trim();
      if (min && !isNaN(Number(min))) {
        range.min = Number(min);
      }
    } else if (!isNaN(Number(priceRange))) {
      range.min = Number(priceRange);
      range.max = Number(priceRange);
    }

    return range;
  }

  // Helper method to format products with enhanced information
  private formatProductWithFilters(product: any): any {
    const price = typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
      ? product.price.toNumber()
      : Number(product.price);

    const salePrice = product.salePrice ? Number(product.salePrice) : null;

    // Calculate discount percentage
    let discountPercent = 0;
    let isOnSale = false;

    if (salePrice && salePrice < price) {
      discountPercent = Math.round(((price - salePrice) / price) * 100);
      isOnSale = true;
    }

    return {
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      shortDescription: product.shortDescription,
      imageUrl: product.images?.[0]?.url || null,
      price,
      salePrice,
      inStock: product.stockStatus === 'IN_STOCK',
      stockQuantity: product.stockQuantity,
      stockStatus: product.stockStatus,
      productType: product.type,
      discountPercent,
      isOnSale,
      created_at: product.createdAt,
      updated_at: product.updatedAt,
      access: product.access, // Include visibility level (PUBLIC/PROTECTED)
      tags: product.tags?.map((tag: any) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
      })) || [],
      categories: product.categories?.map((category: any) => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
      })) || [],
    };
  }

  // Helper method to calculate pagination info
  private calculatePagination(total: number, page: number, limit: number) {
    const totalPages = Math.ceil(total / limit);
    return {
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async getProductDetailsBySlug(slug: string, password?: string): Promise<ProductDetailsDto> {
    // Generate a normalized slug from the input
    const normalizedSlug = slug.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-');

    // Find the product directly by slug
    const product = await this.prisma.product.findFirst({
      where: {
        slug: normalizedSlug
      },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
        ProductAttribute: {
          include: {
            attribute: true,
            values: true,
          },
        },
        variants: {
          include: {
            ProductImage: true,
            attributes: {
              include: {
                value: true,
              },
            },
          },
        },
      },
    });
    // If product doesn't exist, throw NotFoundException
    if (!product) {
      throw new NotFoundException(`Product with slug "${slug}" not found`);
    }

    // Handle access control
    if (product.access === AccessLevel.PRIVATE) {
      throw new NotFoundException(`Product with slug "${slug}" not found`);
    }

    // For protected products, check password
    if (product.access === AccessLevel.PROTECTED) {
      // If no password provided, throw an error requiring password
      if (!password) {
        throw new NotFoundException(`This product is protected. Please provide a password to access it.`);
      }

      // If password doesn't match, throw an error
      if (password !== product.password) {
        throw new NotFoundException(`Invalid password for protected product.`);
      }
    }

    // Return full product details
    return {
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      description: product.description || '',
      shortDescription: product.shortDescription || '',
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      stockQuantity: product.stockQuantity,
      stockStatus: product.stockStatus,
      type: product.type,
      access: product.access,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      images: product.images.map(image => ({
        id: image.id,
        url: image.url,
        position: image.position,
      })),
      categories: product.categories.map(category => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        imageUrl: category.imageUrl,
      })),
      tags: product.tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
      })),
      listings: product.listings.map(listing => ({
        id: listing.id,
        title: listing.title,
        content: listing.content,
      })),
      ProductAttribute: product.ProductAttribute.map(pa => ({
        attribute: {
          id: pa.attribute.id,
          name: pa.attribute.name,
          values: pa.values.map(v => ({
            id: v.id,
            value: v.value,
          })),
        },
        values: pa.values.map(v => ({
          id: v.id,
          value: v.value,
        })),
      })),
      variants: product.variants.map(variant => ({
        id: variant.id,
        sku: variant.sku,
        price: typeof variant.price === 'object' && variant.price !== null && 'toNumber' in variant.price
          ? variant.price.toNumber()
          : Number(variant.price),
        stockQuantity: variant.stockQuantity,
        stockStatus: variant.stockStatus,
        ProductImage: variant.ProductImage.map(image => ({
          id: image.id,
          url: image.url,
          position: image.position,
        })),
        attributes: variant.attributes.map(attr => ({
          value: {
            id: attr.value.id,
            value: attr.value.value,
          },
        })),
      })),
    };
  }

  async getProductDetails(id: number, password?: string): Promise<ProductDetailsDto> {
    // Find the product with all its relationships
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
        ProductAttribute: {
          include: {
            attribute: true,
            values: true,
          },
        },
        variants: {
          include: {
            ProductImage: true,
            attributes: {
              include: {
                value: true,
              },
            },
          },
        },
      },
    });

    // If product doesn't exist, throw NotFoundException
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    // Handle access control
    if (product.access === AccessLevel.PRIVATE) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    // For protected products, check password
    if (product.access === AccessLevel.PROTECTED) {
      // If no password provided, throw an error requiring password
      if (!password) {
        throw new NotFoundException(`This product is protected. Please provide a password to access it.`);
      }

      // If password doesn't match, throw an error
      if (password !== product.password) {
        throw new NotFoundException(`Invalid password for protected product.`);
      }
    }

    // Return full product details
    return {
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      description: product.description || '',
      shortDescription: product.shortDescription || '',
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      stockQuantity: product.stockQuantity,
      stockStatus: product.stockStatus,
      type: product.type,
      access: product.access,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      images: product.images.map(image => ({
        id: image.id,
        url: image.url,
        position: image.position,
      })),
      categories: product.categories.map(category => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        imageUrl: category.imageUrl,
      })),
      tags: product.tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
      })),
      listings: product.listings.map(listing => ({
        id: listing.id,
        title: listing.title,
        content: listing.content,
      })),
      ProductAttribute: product.ProductAttribute.map(pa => ({
        attribute: {
          id: pa.attribute.id,
          name: pa.attribute.name,
          values: pa.values.map(v => ({
            id: v.id,
            value: v.value,
          })),
        },
        values: pa.values.map(v => ({
          id: v.id,
          value: v.value,
        })),
      })),
      variants: product.variants.map(variant => ({
        id: variant.id,
        sku: variant.sku,
        price: typeof variant.price === 'object' && variant.price !== null && 'toNumber' in variant.price
          ? variant.price.toNumber()
          : Number(variant.price),
        stockQuantity: variant.stockQuantity,
        stockStatus: variant.stockStatus,
        ProductImage: variant.ProductImage.map(image => ({
          id: image.id,
          url: image.url,
          position: image.position,
        })),
        attributes: variant.attributes.map(attr => ({
          value: {
            id: attr.value.id,
            value: attr.value.value,
          },
        })),
      })),
    };
  }

  async searchProducts(query: string, limit = 20, page = 1) {
    const skip = (page - 1) * limit;
    const limitInt = parseInt(limit.toString(), 10);

    // Clean and prepare the search query
    const cleanQuery = query.trim().toLowerCase();

    if (!cleanQuery) {
      return {
        data: [],
        pagination: {
          total: 0,
          page: parseInt(page.toString(), 10),
          limit: limitInt
        }
      };
    }

    // Use raw SQL for advanced search with search vector and trigram similarity
    const searchResults = await this.prisma.$queryRaw`
      SELECT
        p.id,
        p.name,
        p.slug,
        p.price,
        p.access,
        (
          -- Full-text search score (highest weight)
          COALESCE(ts_rank(p.search_vector, plainto_tsquery('english', ${cleanQuery})), 0) * 4 +
          -- Trigram similarity for name (very high weight)
          COALESCE(similarity(p.name, ${cleanQuery}), 0) * 5 +
          -- Trigram similarity for normalized name (high weight for fuzzy matching)
          COALESCE(similarity(normalize_text(p.name), normalize_text(${cleanQuery})), 0) * 4 +
          -- Trigram similarity for description
          COALESCE(similarity(COALESCE(p.description, ''), ${cleanQuery}), 0) * 2 +
          -- Trigram similarity for short description
          COALESCE(similarity(COALESCE(p."shortDescription", ''), ${cleanQuery}), 0) * 2 +
          -- Exact match bonus
          CASE
            WHEN LOWER(p.name) LIKE LOWER(${`%${cleanQuery}%`}) THEN 3
            WHEN normalize_text(p.name) LIKE normalize_text(${`%${cleanQuery}%`}) THEN 2
            ELSE 0
          END
        ) as relevance_score
      FROM "Product" p
      WHERE
        p.access != 'PRIVATE'
        AND (
          -- Full-text search using search vector
          p.search_vector @@ plainto_tsquery('english', ${cleanQuery})
          OR
          -- Trigram similarity search with lower threshold for better fuzzy matching
          similarity(p.name, ${cleanQuery}) > 0.1
          OR
          similarity(normalize_text(p.name), normalize_text(${cleanQuery})) > 0.1
          OR
          similarity(COALESCE(p.description, ''), ${cleanQuery}) > 0.1
          OR
          similarity(COALESCE(p."shortDescription", ''), ${cleanQuery}) > 0.1
          OR
          -- Fallback: case-insensitive contains
          LOWER(p.name) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          normalize_text(p.name) LIKE normalize_text(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p.description, '')) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p."shortDescription", '')) LIKE LOWER(${`%${cleanQuery}%`})
        )
      ORDER BY relevance_score DESC, p.name ASC
      LIMIT ${limitInt}
      OFFSET ${skip}
    ` as Array<{
      id: number;
      name: string;
      slug: string;
      price: any;
      access: string;
      relevance_score: number;
    }>;

    // Get total count with the same search criteria
    const totalCountResult = await this.prisma.$queryRaw`
      SELECT COUNT(*) as total
      FROM "Product" p
      WHERE
        p.access != 'PRIVATE'
        AND (
          p.search_vector @@ plainto_tsquery('english', ${cleanQuery})
          OR
          similarity(p.name, ${cleanQuery}) > 0.1
          OR
          similarity(normalize_text(p.name), normalize_text(${cleanQuery})) > 0.1
          OR
          similarity(COALESCE(p.description, ''), ${cleanQuery}) > 0.1
          OR
          similarity(COALESCE(p."shortDescription", ''), ${cleanQuery}) > 0.1
          OR
          LOWER(p.name) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          normalize_text(p.name) LIKE normalize_text(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p.description, '')) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p."shortDescription", '')) LIKE LOWER(${`%${cleanQuery}%`})
        )
    ` as Array<{ total: bigint }>;

    const totalCount = Number(totalCountResult[0]?.total || 0);

    // Get product images for the found products
    const productIds = searchResults.map(p => p.id);
    const productImages = productIds.length > 0 ? await this.prisma.productImage.findMany({
      where: {
        productId: {
          in: productIds
        }
      },
      orderBy: {
        position: 'asc'
      }
    }) : [];

    // Group images by product ID
    const imagesByProduct = productImages.reduce((acc, image) => {
      if (!acc[image.productId!]) {
        acc[image.productId!] = [];
      }
      acc[image.productId!].push(image);
      return acc;
    }, {} as Record<number, typeof productImages>);

    return {
      data: searchResults.map(product => {
        const images = imagesByProduct[product.id] || [];
        return {
          id: product.id.toString(),
          name: product.name,
          slug: product.slug,
          access: product.access, // Include visibility level
          price: {
            price: Number(product.price)
          },
          media: {
            mainMedia: {
              image: {
                url: images[0]?.url || 'https://placehold.co/600x400?text=No+Image'
              }
            },
            items: images.map(image => ({
              image: {
                url: image.url
              }
            }))
          },
          score: Number(product.relevance_score)
        };
      }),
      pagination: {
        total: totalCount,
        page: parseInt(page.toString(), 10),
        limit: limitInt
      }
    };
  }

  async getProductsByMainCategoryAndCategorySlug(
    mainCategorySlug: string,
    categorySlug: string,
    filters: ProductFiltersDto = {}
  ): Promise<EnhancedPaginatedProductsDto> {
    // Find the main category by slug
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { slug: mainCategorySlug },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with slug "${mainCategorySlug}" not found`);
    }

    // Find the category by slug and main category
    const category = await this.prisma.category.findFirst({
      where: {
        slug: categorySlug,
        mainCategoryId: mainCategory.id,
      },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        imageUrl: true,
      },
    });

    if (!category) {
      throw new NotFoundException(`Category with slug "${categorySlug}" not found in main category "${mainCategorySlug}"`);
    }

    // Get all subcategories for this main category with their product counts
    const subcategories = await this.prisma.category.findMany({
      where: { mainCategoryId: mainCategory.id },
      select: {
        id: true,
        name: true,
        slug: true,
        imageUrl: true,
      },
    });

    // Get product counts for each subcategory
    const subcategoriesWithCount = await Promise.all(
      subcategories.map(async (cat) => {
        const count = await this.prisma.productCategories.count({
          where: {
            categoryId: cat.id,
            product: {
              access: {
                not: AccessLevel.PRIVATE
              }
            }
          },
        });

        return {
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
          imageUrl: cat.imageUrl,
          count,
        };
      })
    );

    const page = filters.page || 1;
    const limit = filters.limit || 20;

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause with category filter
    const where = this.buildProductWhereClause(filters, category.id);

    // Build order by clause
    const orderBy = this.buildOrderByClause(filters.sortBy);

    // Get total count of products in this category
    const total = await this.prisma.product.count({ where });

    // Get products for this category with enhanced information
    const products = await this.prisma.product.findMany({
      where,
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        stockQuantity: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        shortDescription: true,
        access: true, // Include access level for visibility
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy,
      skip,
      take,
    });

    // Format the products
    const formattedProducts = products.map(product => this.formatProductWithFilters(product));

    return {
      pagination: this.calculatePagination(total, page, limit),
      data: formattedProducts,
      categories: subcategoriesWithCount,
      category: {
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        imageUrl: category.imageUrl,
      },
      appliedFilters: { ...filters, categoryIds: [category.id] },
    };
  }

  async getProductsByMainCategorySlug(
    mainCategorySlug: string,
    filters: ProductFiltersDto = {}
  ): Promise<EnhancedPaginatedProductsDto> {
    // Find the main category by slug
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { slug: mainCategorySlug },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with slug "${mainCategorySlug}" not found`);
    }

    const page = filters.page || 1;
    const limit = filters.limit || 20;

    // Get all subcategories for this main category with their product counts
    const subcategories = await this.prisma.category.findMany({
      where: { mainCategoryId: mainCategory.id },
      select: {
        id: true,
        name: true,
        slug: true,
        imageUrl: true,
      },
    });

    // Get product counts for each subcategory
    const subcategoriesWithCount = await Promise.all(
      subcategories.map(async (category) => {
        const count = await this.prisma.productCategories.count({
          where: {
            categoryId: category.id,
            product: {
              access: {
                not: AccessLevel.PRIVATE
              }
            }
          },
        });

        return {
          id: category.id,
          name: category.name,
          slug: category.slug,
          imageUrl: category.imageUrl,
          count,
        };
      })
    );

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause with main category filter
    const where = this.buildProductWhereClause(filters, undefined, mainCategory.id);

    // Build order by clause
    const orderBy = this.buildOrderByClause(filters.sortBy);

    // Get total count of products in these categories
    const total = await this.prisma.product.count({ where });

    // Get products for these categories with enhanced information
    const products = await this.prisma.product.findMany({
      where,
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        stockQuantity: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        shortDescription: true,
        access: true, // Include access level for visibility
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy,
      skip,
      take,
    });

    // Format the products
    const formattedProducts = products.map(product => this.formatProductWithFilters(product));

    return {
      pagination: this.calculatePagination(total, page, limit),
      data: formattedProducts,
      categories: subcategoriesWithCount,
      appliedFilters: filters,
    };
  }

  async getProductsByCategorySlug(
    slug: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedProductsDto> {
    // Find the category by slug
    const category = await this.prisma.category.findUnique({
      where: { slug },
    });

    if (!category) {
      throw new NotFoundException(`Category with slug "${slug}" not found`);
    }

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Get total count of products in this category (excluding private products)
    const total = await this.prisma.productCategories.count({
      where: {
        categoryId: category.id,
        product: {
          access: {
            not: AccessLevel.PRIVATE
          }
        }
      },
    });

    // Get products for this category with pagination (excluding private products)
    const products = await this.prisma.product.findMany({
      where: {
        categories: {
          some: {
            id: category.id,
          },
        },
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        stockQuantity: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        shortDescription: true,
        access: true, // Include access level for visibility
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      skip,
      take,
    });

    // Format the products using the standard formatter
    const formattedProducts = products.map(product => this.formatProductWithFilters(product));

    return {
      pagination: {
        total,
        page,
        limit,
      },
      data: formattedProducts,
    };
  }

  async getAllProducts(
    filters: ProductFiltersDto = {}
  ): Promise<PaginatedProductsDto> {
    const page = filters.page || 1;
    const limit = filters.limit || 20;

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause
    const where = this.buildProductWhereClause(filters);

    // Build order by clause
    const orderBy = this.buildOrderByClause(filters.sortBy);

    // Get total count of products
    const total = await this.prisma.product.count({ where });

    // Get products with enhanced information
    const products = await this.prisma.product.findMany({
      where,
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        stockQuantity: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        shortDescription: true,
        access: true, // Include access level for visibility
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy,
      skip,
      take,
    });

    // Format the products
    const formattedProducts = products.map(product => this.formatProductWithFilters(product));

    return {
      pagination: this.calculatePagination(total, page, limit),
      data: formattedProducts,
      appliedFilters: filters,
    };
  }

  async getAllProductNames(): Promise<{ data: { id: number; name: string; slug: string; access: AccessLevel }[] }> {
    // Get all non-private products with minimal fields
    const products = await this.prisma.product.findMany({
      where: {
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        name: true,
        access: true, // Include access level for visibility
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Format the response
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      slug: this.generateSlug(product.name),
      access: product.access
    }));

    return { data: formattedProducts };
  }

  async getWholesaleProducts(
    filters: ProductFiltersDto = {}
  ): Promise<EnhancedPaginatedProductsDto> {
    // Find the "Wholesale" category by name
    const wholesaleCategory = await this.prisma.category.findFirst({
      where: {
        name: {
          equals: 'Wholesale',
          mode: 'insensitive'
        }
      },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        imageUrl: true,
      },
    });

    if (!wholesaleCategory) {
      throw new NotFoundException('Wholesale category not found');
    }

    const page = filters.page || 1;
    const limit = filters.limit || 20;

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause with wholesale category filter
    const where = this.buildProductWhereClause(filters, wholesaleCategory.id);

    // Build order by clause
    const orderBy = this.buildOrderByClause(filters.sortBy);

    // Get total count of products in wholesale category
    const total = await this.prisma.product.count({ where });

    // Get products for wholesale category with enhanced information
    const products = await this.prisma.product.findMany({
      where,
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        stockQuantity: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        shortDescription: true,
        access: true, // Include access level for visibility
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy,
      skip,
      take,
    });

    // Format the products
    const formattedProducts = products.map(product => this.formatProductWithFilters(product));

    return {
      pagination: this.calculatePagination(total, page, limit),
      data: formattedProducts,
      appliedFilters: filters,
      categories: [], // Empty array since this is a single category endpoint
      category: {
        id: wholesaleCategory.id,
        name: wholesaleCategory.name,
        slug: wholesaleCategory.slug,
        description: wholesaleCategory.description,
        imageUrl: wholesaleCategory.imageUrl,
      },
    };
  }

  async getWholesaleFilterSummary() {
    // Find the "Wholesale" category by name
    const wholesaleCategory = await this.prisma.category.findFirst({
      where: {
        name: {
          equals: 'Wholesale',
          mode: 'insensitive'
        }
      },
    });

    if (!wholesaleCategory) {
      throw new NotFoundException('Wholesale category not found');
    }

    return this.getFilterSummary(wholesaleCategory.id);
  }

  // Helper method to get filter summary for building filter UI
  async getFilterSummary(categoryId?: number, mainCategoryId?: number): Promise<FilterSummary> {
    const baseWhere: any = {
      access: {
        not: AccessLevel.PRIVATE
      }
    };

    // Add category filters if specified
    if (categoryId) {
      baseWhere.categories = {
        some: { id: categoryId }
      };
    } else if (mainCategoryId) {
      baseWhere.categories = {
        some: {
          mainCategoryId: mainCategoryId
        }
      };
    }

    // Get total product count
    const totalProducts = await this.prisma.product.count({
      where: baseWhere
    });

    // Get price range
    const priceStats = await this.prisma.product.aggregate({
      where: baseWhere,
      _min: { price: true },
      _max: { price: true }
    });

    // Get available categories with counts
    const categories = await this.prisma.category.findMany({
      where: mainCategoryId ? { mainCategoryId } : {},
      select: {
        id: true,
        name: true,
        slug: true,
        _count: {
          select: {
            ProductCategories: {
              where: {
                product: baseWhere
              }
            }
          }
        }
      }
    });

    // Get available tags with counts
    const tags = await this.prisma.tag.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        _count: {
          select: {
            ProductTags: {
              where: {
                product: baseWhere
              }
            }
          }
        }
      },
      where: {
        ProductTags: {
          some: {
            product: baseWhere
          }
        }
      }
    });

    // Get stock status counts
    const stockStatusCounts = await this.prisma.product.groupBy({
      by: ['stockStatus'],
      where: baseWhere,
      _count: true
    });

    // Get product type counts
    const productTypeCounts = await this.prisma.product.groupBy({
      by: ['type'],
      where: baseWhere,
      _count: true
    });

    // Get on sale count
    const onSaleCount = await this.prisma.product.count({
      where: {
        ...baseWhere,
        salePrice: { not: null }
      }
    });

    return {
      totalProducts,
      priceRange: {
        min: Number(priceStats._min.price) || 0,
        max: Number(priceStats._max.price) || 0
      },
      availableCategories: categories.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        count: cat._count.ProductCategories
      })),
      availableTags: tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        count: tag._count.ProductTags
      })),
      stockStatusCounts: {
        inStock: stockStatusCounts.find(s => s.stockStatus === 'IN_STOCK')?._count || 0,
        outOfStock: stockStatusCounts.find(s => s.stockStatus === 'OUT_OF_STOCK')?._count || 0,
        onBackorder: stockStatusCounts.find(s => s.stockStatus === 'ON_BACKORDER')?._count || 0
      },
      productTypeCounts: {
        simple: productTypeCounts.find(p => p.type === 'SIMPLE')?._count || 0,
        variable: productTypeCounts.find(p => p.type === 'VARIABLE')?._count || 0,
        grouped: productTypeCounts.find(p => p.type === 'GROUPED')?._count || 0,
        external: productTypeCounts.find(p => p.type === 'EXTERNAL')?._count || 0
      },
      onSaleCount
    };
  }

  // Helper method to get filter summary for main category
  async getMainCategoryFilterSummary(mainCategorySlug: string): Promise<FilterSummary> {
    // Find the main category by slug
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { slug: mainCategorySlug },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with slug "${mainCategorySlug}" not found`);
    }

    return this.getFilterSummary(undefined, mainCategory.id);
  }
}
