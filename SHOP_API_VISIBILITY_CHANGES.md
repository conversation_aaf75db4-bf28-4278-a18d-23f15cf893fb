# Shop API Visibility Changes Documentation

This document outlines all changes made to the Shop API to support product visibility levels and password protection for protected products.

## Overview

The Shop API has been enhanced to include product visibility information in all product responses and implement proper password protection for protected products.

## Product Visibility Levels

Products can have three visibility levels:
- **PUBLIC**: Visible to all users, full product information returned
- **PROTECTED**: Visible to all users, but requires password to access full details
- **PRIVATE**: Not visible in any public API responses

## Changes to API Responses

### 1. Product List Responses

All endpoints that return product lists now include the `access` field showing the visibility level.

#### Affected Endpoints:
- `GET /api/shop/products` - Get all products
- `GET /api/shop/categories/:catId/products` - Get products by category
- `GET /api/shop/wholesale` - Get wholesale products
- `GET /api/shop/:mainCategorySlug` - Get products by main category
- `GET /api/shop/:mainCategorySlug/:categorySlug` - Get products by main and sub category
- `GET /api/shop/products/search` - Search products
- `GET /api/shop/products/names` - Get all product names

#### Response Changes:

**Before:**
```json
{
  "data": [
    {
      "id": 1,
      "sku": "PROD-001",
      "name": "Sample Product",
      "slug": "sample-product",
      "price": 29.99,
      "salePrice": null,
      "inStock": true,
      "stockQuantity": 10,
      "stockStatus": "IN_STOCK",
      "productType": "SIMPLE",
      "imageUrl": "https://example.com/image.jpg",
      "shortDescription": "A sample product",
      "discountPercent": 0,
      "isOnSale": false,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z",
      "tags": [],
      "categories": []
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 20
  }
}
```

**After:**
```json
{
  "data": [
    {
      "id": 1,
      "sku": "PROD-001",
      "name": "Sample Product",
      "slug": "sample-product",
      "price": 29.99,
      "salePrice": null,
      "inStock": true,
      "stockQuantity": 10,
      "stockStatus": "IN_STOCK",
      "productType": "SIMPLE",
      "imageUrl": "https://example.com/image.jpg",
      "shortDescription": "A sample product",
      "discountPercent": 0,
      "isOnSale": false,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z",
      "access": "PUBLIC",
      "tags": [],
      "categories": []
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 20
  }
}
```

### 2. Search Results

Search results now include the `access` field for each product.

#### Endpoint: `GET /api/shop/products/search`

**Before:**
```json
{
  "data": [
    {
      "id": "1",
      "name": "Sample Product",
      "slug": "sample-product",
      "price": {
        "price": 29.99
      },
      "media": {
        "mainMedia": {
          "image": {
            "url": "https://example.com/image.jpg"
          }
        },
        "items": []
      },
      "score": 0.95
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 20
  }
}
```

**After:**
```json
{
  "data": [
    {
      "id": "1",
      "name": "Sample Product",
      "slug": "sample-product",
      "access": "PUBLIC",
      "price": {
        "price": 29.99
      },
      "media": {
        "mainMedia": {
          "image": {
            "url": "https://example.com/image.jpg"
          }
        },
        "items": []
      },
      "score": 0.95
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 20
  }
}
```

### 3. Product Names Response

The product names endpoint now includes the `access` field.

#### Endpoint: `GET /api/shop/products/names`

**Before:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Sample Product",
      "slug": "sample-product"
    }
  ]
}
```

**After:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Sample Product",
      "slug": "sample-product",
      "access": "PUBLIC"
    }
  ]
}
```

## Changes to Product Detail Endpoints

### 3. Product Details by Slug

#### Endpoint: `GET /api/shop/products/slug/:slug`

This endpoint now requires password protection for PROTECTED products.

#### Request Parameters:
- `slug` (path parameter): Product slug
- `password` (query parameter, optional): Password for protected products

#### Behavior Changes:

**For PUBLIC products:**
- No password required
- Full product details returned
- `access` field shows "PUBLIC"

**For PROTECTED products:**
- Password required via query parameter
- If no password provided: Returns 404 with message "This product is protected. Please provide a password to access it."
- If wrong password provided: Returns 404 with message "Invalid password for protected product."
- If correct password provided: Full product details returned with `access` field showing "PROTECTED"

**For PRIVATE products:**
- Not accessible via public API
- Returns 404 with message "Product with slug '{slug}' not found"

#### Example Requests:

**Public Product:**
```
GET /api/shop/products/slug/public-product
```

**Protected Product (with password):**
```
GET /api/shop/products/slug/protected-product?password=secret123
```

**Protected Product (without password) - Returns 404:**
```
GET /api/shop/products/slug/protected-product
```

#### Response Format:
The response format remains the same as before, but now includes the `access` field:

```json
{
  "id": 1,
  "sku": "PROD-001",
  "name": "Sample Product",
  "slug": "sample-product",
  "description": "Full product description",
  "shortDescription": "Short description",
  "price": 29.99,
  "salePrice": null,
  "stockQuantity": 10,
  "stockStatus": "IN_STOCK",
  "type": "SIMPLE",
  "access": "PUBLIC",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z",
  "images": [],
  "categories": [],
  "tags": [],
  "listings": [],
  "ProductAttribute": [],
  "variants": []
}
```

## Error Responses

### New Error Messages for Protected Products:

1. **Missing Password:**
   ```json
   {
     "statusCode": 404,
     "message": "This product is protected. Please provide a password to access it.",
     "error": "Not Found"
   }
   ```

2. **Invalid Password:**
   ```json
   {
     "statusCode": 404,
     "message": "Invalid password for protected product.",
     "error": "Not Found"
   }
   ```

## Frontend Implementation Guidelines

### 1. Displaying Visibility Status

Use the `access` field to show appropriate indicators in the frontend:

```javascript
function getVisibilityIndicator(product) {
  switch (product.access) {
    case 'PUBLIC':
      return '🌐 Public';
    case 'PROTECTED':
      return '🔒 Protected';
    default:
      return '';
  }
}
```

### 2. Handling Protected Products

When a user clicks on a protected product, prompt for password:

```javascript
async function viewProtectedProduct(slug) {
  try {
    // First try without password
    const response = await fetch(`/api/shop/products/slug/${slug}`);
    if (response.ok) {
      return await response.json();
    }
    
    // If 404, check if it's a protected product
    const error = await response.json();
    if (error.message.includes('protected')) {
      const password = prompt('This product is protected. Please enter the password:');
      if (password) {
        const protectedResponse = await fetch(`/api/shop/products/slug/${slug}?password=${encodeURIComponent(password)}`);
        if (protectedResponse.ok) {
          return await protectedResponse.json();
        } else {
          const protectedError = await protectedResponse.json();
          alert(protectedError.message);
        }
      }
    }
  } catch (error) {
    console.error('Error fetching product:', error);
  }
}
```

### 3. Product List Filtering

Filter products by visibility level if needed:

```javascript
function filterByVisibility(products, visibilityLevel) {
  return products.filter(product => product.access === visibilityLevel);
}

// Get only public products
const publicProducts = filterByVisibility(allProducts, 'PUBLIC');

// Get only protected products
const protectedProducts = filterByVisibility(allProducts, 'PROTECTED');
```

## Summary of Changes

1. **Added `access` field** to all product list responses showing visibility level
2. **Enhanced password protection** for protected products in detail endpoints
3. **Improved error messages** for missing or invalid passwords
4. **Updated search results** to include visibility information
5. **Maintained backward compatibility** - existing fields remain unchanged

All changes are backward compatible. Existing frontend code will continue to work, but should be updated to handle the new `access` field and password protection for protected products.
