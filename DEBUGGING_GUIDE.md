# Store API Debugging Guide

This guide explains the comprehensive debugging features added to help identify and resolve parsing issues with the Store API pagination and search functionality.

## 🔧 Debugging Features Added

### 1. **Enhanced ValidationPipe Configuration** (`src/main.ts`)
```typescript
app.useGlobalPipes(new ValidationPipe({
  transform: true,                    // Enable automatic transformation
  transformOptions: {
    enableImplicitConversion: true,   // Convert string "1" to number 1
  },
  whitelist: true,                    // Strip unknown properties
  forbidNonWhitelisted: false,        // Don't throw errors for extra props
  disableErrorMessages: false,        // Show detailed error messages
  validationError: {
    target: false,                    // Don't include target in errors
    value: false,                     // Don't include value in errors
  },
}));
```

### 2. **Debug Middleware** (`src/store/middleware/debug.middleware.ts`)
Logs all incoming requests to store endpoints:
- Raw query parameters and their types
- Request method, URL, and path
- Headers and body content
- Parameter parsing details

### 3. **DTO Transform Debugging** (`src/store/dto/pagination-query.dto.ts`)
Each parameter now logs its transformation:
```typescript
@Transform(({ value }) => {
  console.log('🔍 [DTO Transform] page value:', value, 'type:', typeof value);
  return value;
})
```

### 4. **Controller Debugging** (`src/store/store.controller.ts`)
Logs parameter processing in both endpoints:
- Raw query parameters received
- Parameter types before and after processing
- Final values passed to service

### 5. **Service Debugging** (`src/store/store.service.ts`)
Comprehensive logging in both service methods:
- Input parameter validation
- Pagination calculations
- Search query construction
- Database query results
- Final response structure

## 🚀 How to Use the Debugging

### Step 1: Start the Server
```bash
yarn start:dev
```

### Step 2: Make API Calls
The debugging will automatically log information for any store API calls:

```bash
# Test basic pagination
curl "http://localhost:3000/api/store/products?page=1&limit=5"

# Test search functionality
curl "http://localhost:3000/api/store/products?search=coffee"

# Test category products with search
curl "http://localhost:3000/api/store/categories/1/products?page=1&limit=3&search=tea"
```

### Step 3: Use the Test Script
Run the comprehensive test script:
```bash
node test-store-api.js
```

## 📊 Debug Output Examples

### Successful Request
```
🔍 [Debug Middleware] Store API Request:
📡 Method: GET
📡 URL: /store/products?page=2&limit=5&search=coffee
📡 Query Params (raw): { page: '2', limit: '5', search: 'coffee' }
📡 Query[page]: 2 (type: string)
📡 Query[limit]: 5 (type: string)
📡 Query[search]: coffee (type: string)

🔍 [DTO Transform] page value: 2 type: number
🔍 [DTO Transform] limit value: 5 type: number
🔍 [DTO Transform] search value: coffee type: string

🔍 [Store Controller] getAllProducts - Processed params: {
  page: 2,
  limit: 5,
  search: 'coffee',
  pageType: 'number',
  limitType: 'number',
  searchType: 'string'
}

🔍 [Store Service] getAllProducts - Where clause: {
  "OR": [
    { "name": { "contains": "coffee", "mode": "insensitive" } },
    { "sku": { "contains": "coffee", "mode": "insensitive" } },
    { "shortDescription": { "contains": "coffee", "mode": "insensitive" } },
    { "description": { "contains": "coffee", "mode": "insensitive" } }
  ]
}

🔍 [Store Service] getAllProducts - Total count: 3
🔍 [Store Service] getAllProducts - Results: {
  totalProducts: 3,
  totalPages: 1,
  hasNext: false,
  hasPrev: true
}
```

### Validation Error
```
🔍 [Debug Middleware] Store API Request:
📡 URL: /store/products?page=0&limit=101

🔍 [DTO Transform] page value: 0 type: number
🔍 [DTO Transform] limit value: 101 type: number

Response: {
  "statusCode": 400,
  "message": [
    "Page must be at least 1",
    "Limit must not exceed 100"
  ],
  "error": "Bad Request"
}
```

## 🔍 Common Issues to Look For

### 1. **Parameter Type Issues**
- Check if strings are being converted to numbers properly
- Verify that the `@Type(() => Number)` decorator is working
- Look for `enableImplicitConversion: true` in ValidationPipe

### 2. **Search Parameter Problems**
- Verify search string is not empty or just whitespace
- Check if special characters are being handled correctly
- Ensure case-insensitive search is working

### 3. **Database Query Issues**
- Review the generated WHERE clause in logs
- Check if the Prisma query structure is correct
- Verify that relationships (ProductCategories) are properly joined

### 4. **Pagination Calculation Errors**
- Verify skip/take calculations: `skip = (page - 1) * limit`
- Check totalPages calculation: `Math.ceil(total / limit)`
- Ensure hasNext/hasPrev flags are correct

## 🛠️ Troubleshooting Steps

1. **Check the Debug Middleware logs** - Are parameters being received correctly?
2. **Verify DTO Transform logs** - Are types being converted properly?
3. **Review Controller logs** - Are processed parameters correct?
4. **Examine Service logs** - Is the database query constructed correctly?
5. **Check Response Structure** - Does the final response match expectations?

## 🧪 Test Scenarios

The `test-store-api.js` script covers:
- ✅ Basic pagination (default and custom)
- ✅ Search functionality (various terms)
- ✅ Search with pagination
- ✅ Category-specific searches
- ✅ Invalid parameters (page=0, limit=101)
- ✅ Type conversion (page="abc", limit="xyz")
- ✅ Non-existent categories
- ✅ Edge cases and error conditions

## 📝 Removing Debug Logs

When debugging is complete, you can remove the debug logs by:

1. **Remove Transform decorators** from `pagination-query.dto.ts`
2. **Remove console.log statements** from controller and service
3. **Remove or disable DebugMiddleware** from store module
4. **Keep the enhanced ValidationPipe** configuration for better error handling

The debugging infrastructure provides comprehensive visibility into the request processing pipeline, making it easy to identify where parsing or validation issues occur.
