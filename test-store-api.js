// Simple test script to debug Store API pagination and search
const baseUrl = 'http://localhost:3000';

async function testAPI(endpoint, description) {
  console.log(`\n🧪 Testing: ${description}`);
  console.log(`📡 URL: ${baseUrl}${endpoint}`);
  
  try {
    const response = await fetch(`${baseUrl}${endpoint}`);
    const data = await response.json();
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Response:`, JSON.stringify(data, null, 2));
    
    if (data.pagination) {
      console.log(`📄 Pagination Summary:`, {
        total: data.pagination.total,
        page: data.pagination.page,
        limit: data.pagination.limit,
        totalPages: data.pagination.totalPages,
        hasNext: data.pagination.hasNext,
        hasPrev: data.pagination.hasPrev
      });
    }
    
    if (data.searchTerm !== undefined) {
      console.log(`🔍 Search Term:`, data.searchTerm);
    }
    
    if (data.data) {
      console.log(`📦 Products Count:`, data.data.length);
    }
    
  } catch (error) {
    console.log(`❌ Error:`, error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Store API Debug Tests...');
  
  // Test basic pagination
  await testAPI('/store/products', 'Get all products (default pagination)');
  await testAPI('/store/products?page=1', 'Get all products (page 1)');
  await testAPI('/store/products?page=1&limit=5', 'Get all products (page 1, limit 5)');
  
  // Test search functionality
  await testAPI('/store/products?search=coffee', 'Search products for "coffee"');
  await testAPI('/store/products?search=COFFEE', 'Search products for "COFFEE" (uppercase)');
  await testAPI('/store/products?search=premium', 'Search products for "premium"');
  
  // Test search with pagination
  await testAPI('/store/products?search=coffee&page=1&limit=2', 'Search "coffee" with pagination');
  
  // Test category endpoints (assuming category ID 1 exists)
  await testAPI('/store/categories/1/products', 'Get products from category 1 (default pagination)');
  await testAPI('/store/categories/1/products?page=1&limit=3', 'Get products from category 1 (page 1, limit 3)');
  await testAPI('/store/categories/1/products?search=tea', 'Search "tea" in category 1');
  
  // Test edge cases
  await testAPI('/store/products?page=0', 'Invalid page (0)');
  await testAPI('/store/products?limit=101', 'Invalid limit (101)');
  await testAPI('/store/products?page=abc', 'Invalid page (abc)');
  await testAPI('/store/products?limit=xyz', 'Invalid limit (xyz)');
  
  // Test non-existent category
  await testAPI('/store/categories/999/products', 'Non-existent category (999)');
  
  console.log('\n✅ All tests completed!');
}

// Run the tests
runTests().catch(console.error);
