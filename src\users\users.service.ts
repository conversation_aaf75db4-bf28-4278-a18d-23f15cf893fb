import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { 
  UserPaginationQueryDto, 
  PaginatedUsersResponseDto, 
  UserListItemDto,
  UserPaginationDto 
} from './dto/user-pagination.dto';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async getAllUsers(query: UserPaginationQueryDto): Promise<PaginatedUsersResponseDto> {
    const { page = 1, limit = 30, search } = query;
    
    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause for search
    const where: any = {};
    if (search && search.trim()) {
      const searchTerm = search.trim();
      where.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { email: { contains: searchTerm, mode: 'insensitive' } },
        { firstName: { contains: searchTerm, mode: 'insensitive' } },
        { lastName: { contains: searchTerm, mode: 'insensitive' } },
        // Search in combined first and last name
        {
          AND: [
            { firstName: { not: null } },
            { lastName: { not: null } },
          ],
          OR: [
            {
              firstName: {
                contains: searchTerm.split(' ')[0],
                mode: 'insensitive'
              }
            },
            {
              lastName: {
                contains: searchTerm.split(' ').slice(1).join(' ') || searchTerm,
                mode: 'insensitive'
              }
            }
          ]
        }
      ];
    }

    // Get total count
    const total = await this.prisma.user.count({ where });

    // Get users with pagination
    const users = await this.prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        email: true,
        firstName: true,
        lastName: true,
        isAdmin: true,
        isSuperAdmin: true,
        isVerified: true,
        isBanned: true,
        createdAt: true,
        lastLogin: true,
        points: true,
        city: true,
        country: true,
        companyName: true,
      },
      orderBy: [
        { createdAt: 'desc' },
        { id: 'asc' }
      ],
      skip,
      take,
    });

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    
    const pagination: UserPaginationDto = {
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    // Format users
    const formattedUsers: UserListItemDto[] = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isAdmin: user.isAdmin,
      isSuperAdmin: user.isSuperAdmin,
      isVerified: user.isVerified,
      isBanned: user.isBanned,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      points: user.points,
      city: user.city,
      country: user.country,
      companyName: user.companyName,
    }));

    return {
      pagination,
      data: formattedUsers,
      searchTerm: search?.trim() || undefined,
    };
  }

  async getUserById(id: number): Promise<UserListItemDto | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        firstName: true,
        lastName: true,
        isAdmin: true,
        isSuperAdmin: true,
        isVerified: true,
        isBanned: true,
        createdAt: true,
        lastLogin: true,
        points: true,
        city: true,
        country: true,
        companyName: true,
      },
    });

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isAdmin: user.isAdmin,
      isSuperAdmin: user.isSuperAdmin,
      isVerified: user.isVerified,
      isBanned: user.isBanned,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      points: user.points,
      city: user.city,
      country: user.country,
      companyName: user.companyName,
    };
  }
}
