import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger, LogLevel } from '@nestjs/common';
import { AppModule } from './app.module';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import * as express from 'express';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  const port = process.env.PORT || 3300;
  const nodeEnv = process.env.NODE_ENV || 'development';

  // Configure logger based on environment
  const logLevels: LogLevel[] = nodeEnv === 'production'
    ? ['error', 'warn', 'log']
    : ['debug', 'error', 'warn', 'log'];

  const app = await NestFactory.create(AppModule, { logger: logLevels });

  app.useGlobalPipes(new ValidationPipe());

  app.setGlobalPrefix('api');

  // Ensure upload directories exist
  const uploadDirs = [
    './uploads',
    './uploads/products',
    './uploads/variants',
    './uploads/categories',
    './uploads/blogs',
    './uploads/temp'
  ];

  uploadDirs.forEach(dir => {
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
      logger.log(`Created upload directory: ${dir}`);
    }
  });

  // Serve static files for uploaded images
  // Support both /api/uploads (for main domain) and /uploads (for static subdomain)
  app.use('/api/uploads', express.static(join(process.cwd(), 'uploads')));
  app.use('/uploads', express.static(join(process.cwd(), 'uploads')));

  // Configure CORS based on environment
  const corsOrigins = nodeEnv === 'production'
    ? process.env.CORS_ORIGINS?.split(',') || [
        'https://www.omgpackagings.com',
        'https://omgpackagings.com',
        'https://api.omgpackagings.com'
      ]
    : ['http://localhost:3000', 'http://localhost:3001'];

  app.enableCors({
    origin: corsOrigins,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
    exposedHeaders: ['Content-Range', 'X-Content-Range'],
  });

  // Log all registered routes in non-production environments
  if (nodeEnv !== 'production') {
    const server = app.getHttpServer();
    const router = server._events.request._router;

    logger.log('=== REGISTERED ROUTES ===');
    router.stack.forEach((layer: any) => {
      if (layer.route) {
        const path = layer.route.path;
        const methods = Object.keys(layer.route.methods).map((method: string) => method.toUpperCase());
        logger.log(`${methods.join(', ')} ${path}`);
      }
    });
    logger.log('=========================');
  }

  await app.listen(port);
  logger.log(`Application is running in ${nodeEnv} mode on port ${port}`);
}
bootstrap();
