import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateMainCategoryDto } from './dto/create-main-category.dto';
import { UpdateMainCategoryDto } from './dto/update-main-category.dto';
import { AddCategoriesToMainCategoryDto } from './dto/add-categories-to-main-category.dto';
import { Category, MainCategory, Product } from '@prisma/client';

@Injectable()
export class StoreService {
  constructor(private prisma: PrismaService) {}

  // Category Management
  async createCategory(createCategoryDto: CreateCategoryDto): Promise<Category> {
    return this.prisma.category.create({
      data: createCategoryDto,
    });
  }

  async getAllCategories(): Promise<Category[]> {
    return this.prisma.category.findMany();
  }

  async getCategoryById(id: number): Promise<Category> {
    const category = await this.prisma.category.findUnique({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    return category;
  }

  async updateCategory(id: number, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    try {
      return await this.prisma.category.update({
        where: { id },
        data: updateCategoryDto,
      });
    } catch (error) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
  }

  async deleteCategory(id: number): Promise<Category> {
    try {
      // First delete all relations in ProductCategories table
      await this.prisma.productCategories.deleteMany({
        where: {
          categoryId: id,
        },
      });

      // Then delete the category
      return await this.prisma.category.delete({
        where: { id },
      });
    } catch (error) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
  }

  // MainCategory Management
  async createMainCategory(createMainCategoryDto: CreateMainCategoryDto) {
    const { categoryIds = [], ...mainCategoryData } = createMainCategoryDto;

    try {
      // Create the main category
      const mainCategory = await this.prisma.mainCategory.create({
        data: {
          ...mainCategoryData,
          ...(categoryIds.length > 0
            ? {
                categories: {
                  connect: categoryIds.map(id => ({ id })),
                },
              }
            : {}),
        },
        include: {
          categories: true,
        },
      });

      return mainCategory;
    } catch (error) {
      console.error('Error creating main category:', error);
      throw error;
    }
  }

  async getAllMainCategories() {
    return this.prisma.mainCategory.findMany({
      include: {
        categories: true,
      },
    });
  }

  async getMainCategoryById(id: number) {
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { id },
      include: {
        categories: true,
      },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with ID ${id} not found`);
    }

    return mainCategory;
  }

  async updateMainCategory(id: number, updateMainCategoryDto: UpdateMainCategoryDto) {
    const { categoryIds, ...mainCategoryData } = updateMainCategoryDto;

    try {
      // Update the main category
      const mainCategory = await this.prisma.mainCategory.update({
        where: { id },
        data: {
          ...mainCategoryData,
          ...(categoryIds
            ? {
                categories: {
                  set: categoryIds.map(id => ({ id })),
                },
              }
            : {}),
        },
        include: {
          categories: true,
        },
      });

      return mainCategory;
    } catch (error) {
      throw new NotFoundException(`Main category with ID ${id} not found`);
    }
  }

  async addCategoriesToMainCategory(id: number, addCategoriesDto: AddCategoriesToMainCategoryDto) {
    const { categoryIds } = addCategoriesDto;

    try {
      // Check if the main category exists
      const mainCategory = await this.prisma.mainCategory.findUnique({
        where: { id },
      });

      if (!mainCategory) {
        throw new NotFoundException(`Main category with ID ${id} not found`);
      }

      // Update categories for the main category
      await this.prisma.mainCategory.update({
        where: { id },
        data: {
          categories: {
            connect: categoryIds.map(categoryId => ({ id: categoryId })),
          },
        },
      });

      // Return the updated main category with categories
      return this.prisma.mainCategory.findUnique({
        where: { id },
        include: {
          categories: true,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error adding categories to main category:', error);
      throw error;
    }
  }

  async deleteMainCategory(id: number) {
    try {
      // First update all categories to remove the mainCategoryId
      await this.prisma.category.updateMany({
        where: {
          mainCategoryId: id,
        },
        data: {
          mainCategoryId: null,
        },
      });

      // Then delete the main category
      return await this.prisma.mainCategory.delete({
        where: { id },
        include: {
          categories: true,
        },
      });
    } catch (error) {
      throw new NotFoundException(`Main category with ID ${id} not found`);
    }
  }

  // Product Management
  async createProduct(createProductDto: CreateProductDto): Promise<Product> {
    const {
      categoryIds = [],
      images = [],
      tags = [],
      listings = [],
      ...productData
    } = createProductDto;

    try {
      // 1) Create the product with images
      // Filter images to only include fields that exist in ProductImage model
      const filteredImages = images.map(image => ({
        url: image.url,
        position: image.position || 0,
      }));

      const product = await this.prisma.product.create({
        data: {
          ...productData,
          // Generate a slug from the name to ensure uniqueness
          slug: productData.name
            .toLowerCase()
            .trim()
            .replace(/\s+/g, '-')           // Replace spaces with hyphens
            .replace(/[^a-z0-9-]/g, '')     // Remove special characters except hyphens
            .replace(/-+/g, '-')            // Replace multiple hyphens with single hyphen
            .replace(/^-+|-+$/g, ''),       // Remove leading/trailing hyphens
          ...(filteredImages.length > 0
            ? {
                images: {
                  create: filteredImages,
                },
              }
            : {}),
          ...(categoryIds.length > 0
            ? {
                categories: {
                  connect: categoryIds.map(id => ({ id })),
                },
              }
            : {}),
          // Tags will be handled separately to avoid conflicts
        },
      });

      // 2) Create category relationships with skipDuplicates
      if (categoryIds.length > 0) {
        await this.prisma.productCategories.createMany({
          data: categoryIds.map((categoryId) => ({
            productId: product.id,
            categoryId,
          })),
          skipDuplicates: true,
        });
      }

      // 3) Create tag relationships if needed
      console.log(`Creating product - Processing tags:`, tags);
      if (tags && tags.length > 0) {
        // Process tags one by one to handle duplicates properly
        for (const tagName of tags) {
          try {
            // First try to find existing tag
            let tag = await this.prisma.tag.findUnique({
              where: { name: tagName }
            });

            // If tag doesn't exist, create it
            if (!tag) {
              const tagSlug = tagName
                .toLowerCase()
                .trim()
                .replace(/\s+/g, '-')           // Replace spaces with hyphens
                .replace(/[^a-z0-9-]/g, '')     // Remove special characters except hyphens
                .replace(/-+/g, '-')            // Replace multiple hyphens with single hyphen
                .replace(/^-+|-+$/g, '');       // Remove leading/trailing hyphens

              tag = await this.prisma.tag.create({
                data: {
                  name: tagName,
                  slug: tagSlug
                }
              });
            }

            // Create the relationship using Prisma's many-to-many relation
            try {
              await this.prisma.product.update({
                where: { id: product.id },
                data: {
                  tags: {
                    connect: { id: tag.id }
                  }
                }
              });
              console.log(`Created relationship between product ${product.id} and tag ${tag.id}`);
            } catch (relationError) {
              // Ignore if relationship already exists
              if (relationError.code !== 'P2002') {
                throw relationError;
              }
            }
          } catch (tagError) {
            // If tag creation fails due to duplicate slug, try to find the existing tag
            if (tagError.code === 'P2002') {
              const existingTag = await this.prisma.tag.findUnique({
                where: { name: tagName }
              });

              if (existingTag) {
                // Create the relationship with existing tag
                try {
                  await this.prisma.product.update({
                    where: { id: product.id },
                    data: {
                      tags: {
                        connect: { id: existingTag.id }
                      }
                    }
                  });
                  console.log(`Created relationship between product ${product.id} and existing tag ${existingTag.id}`);
                } catch (relationError) {
                  console.error(`Error creating relationship with existing tag:`, relationError);
                  // Ignore if relationship already exists
                  if (relationError.code !== 'P2002') {
                    throw relationError;
                  }
                }
              }
            } else {
              throw tagError;
            }
          }
        }
      }
      console.log(`Finished processing tags for product creation ${product.id}`);

      // 4) Create listings if provided
      if (listings.length > 0) {
        await this.prisma.listing.createMany({
          data: listings.map(listing => ({
            title: listing.title,
            content: listing.content,
            productId: product.id
          })),
        });
      }

      // 5) Return the complete product with relationships
      return await this.prisma.product.findUnique({
        where: { id: product.id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
        },
      });
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  async getAllProducts(page: number = 1, limit: number = 20, search?: string) {
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause for search
    const where: any = {};
    if (search && search.trim()) {
      const searchTerm = search.trim();
      where.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { sku: { contains: searchTerm, mode: 'insensitive' } },
        { shortDescription: { contains: searchTerm, mode: 'insensitive' } },
        { description: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    // Get total count with search filter
    const total = await this.prisma.product.count({ where });

    // Get products with pagination and search
    const products = await this.prisma.product.findMany({
      where,
      select: {
        id: true,
        name: true,
        shortDescription: true,
        price: true,
        sku: true,
        stockQuantity: true,
        stockStatus: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take,
    });

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      data: products,
      searchTerm: search?.trim() || null,
    };
  }

  async getProductById(id: number): Promise<Product> {
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
        ProductAttribute: {
          include: {
            attribute: true,
            values: true,
          },
        },
        variants: {
          include: {
            ProductImage: true,
            attributes: {
              include: {
                value: true,
              },
            },
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return product;
  }

  async getProductsByCategory(categoryId: number, page: number = 1, limit: number = 20, search?: string) {
    const category = await this.prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`);
    }

    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause with category filter and search
    const where: any = {
      ProductCategories: {
        some: {
          categoryId: categoryId,
        },
      },
    };

    // Add search filter if provided
    if (search && search.trim()) {
      const searchTerm = search.trim();
      where.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { sku: { contains: searchTerm, mode: 'insensitive' } },
        { shortDescription: { contains: searchTerm, mode: 'insensitive' } },
        { description: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    // Get total count of products in this category with search filter
    const total = await this.prisma.product.count({ where });

    // Get products with pagination and search
    const products = await this.prisma.product.findMany({
      where,
      select: {
        id: true,
        name: true,
        shortDescription: true,
        price: true,
        sku: true,
        stockQuantity: true,
        stockStatus: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        categories: {
          select: {
            id: true,
            name: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
          },
        },
        // Include product attributes and variants for GROUPED products
        ProductAttribute: {
          select: {
            id: true,
            attribute: {
              select: {
                id: true,
                name: true,
              },
            },
            values: {
              select: {
                id: true,
                value: true,
              },
            },
          },
        },
        variants: {
          select: {
            id: true,
            sku: true,
            price: true,
            stockQuantity: true,
            stockStatus: true,
            ProductImage: {
              take: 1,
              select: {
                url: true,
              },
            },
            attributes: {
              select: {
                value: {
                  select: {
                    id: true,
                    value: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take,
    });

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      data: products,
      category: {
        id: category.id,
        name: category.name,
      },
      searchTerm: search?.trim() || null,
    };
  }

  async updateProduct(id: number, updateProductDto: UpdateProductDto): Promise<Product> {
    // Pull out everything we don't want in `data`
    const {
      categoryIds = [],
      images: rawImages = [],
      tags,                  // Don't set default, keep as undefined if not provided
      listings = [],
      deleteListingIds = [],
      categories,            // ignore if present
      id: _ignoreId,
      createdAt,
      updatedAt,
      productAttributes,      // extract these fields
      variants,              // extract these fields
      ...allowedFields       // now only { sku?, name?, description?, … }
    } = updateProductDto;

    // 0) Ensure it exists
    const existing = await this.prisma.product.findUnique({ where: { id } });
    if (!existing) throw new NotFoundException(`Product with ID ${id} not found`);

    // 1) Prepare a "clean" images array
    const images = rawImages.map(({ url, position }) => ({ url, position }));

    // 2) Update scalars + replace images
    await this.prisma.product.update({
      where: { id },
      data: {
        ...allowedFields,
        ...(images.length > 0
          ? {
              images: {
                deleteMany: {},   // remove old
                create: images,   // add new
              },
            }
          : {}),
        ...(categoryIds.length > 0
          ? {
              categories: {
                set: categoryIds.map(id => ({ id })),
              },
            }
          : {}),
      },
    });

    // 3) Rebuild category links in explicit join table
    await this.prisma.productCategories.deleteMany({ where: { productId: id } });
    if (categoryIds.length > 0) {
      await this.prisma.productCategories.createMany({
        data: categoryIds.map((categoryId) => ({ productId: id, categoryId })),
        skipDuplicates: true,
      });
    }

    // 4) Handle tags - always process tags array (even if empty to clear existing tags)
    if (tags !== undefined) {
      console.log(`Processing tags for product ${id}:`, tags);

      // First, disconnect all existing tags
      await this.prisma.product.update({
        where: { id },
        data: {
          tags: {
            set: [] // This disconnects all existing tags
          }
        }
      });

      // Create new tag relationships if tags are provided
      if (tags && tags.length > 0) {
        for (const tagName of tags) {
          try {
            console.log(`Processing tag: "${tagName}"`);

            // First try to find existing tag
            let tag = await this.prisma.tag.findUnique({
              where: { name: tagName }
            });

            // If tag doesn't exist, create it
            if (!tag) {
              const tagSlug = tagName
                .toLowerCase()
                .trim()
                .replace(/\s+/g, '-')           // Replace spaces with hyphens
                .replace(/[^a-z0-9-]/g, '')     // Remove special characters except hyphens
                .replace(/-+/g, '-')            // Replace multiple hyphens with single hyphen
                .replace(/^-+|-+$/g, '');       // Remove leading/trailing hyphens

              console.log(`Creating new tag: "${tagName}" with slug: "${tagSlug}"`);

              tag = await this.prisma.tag.create({
                data: {
                  name: tagName,
                  slug: tagSlug
                }
              });
            } else {
              console.log(`Found existing tag: "${tagName}" with ID: ${tag.id}`);
            }

            // Create the relationship using Prisma's many-to-many relation
            await this.prisma.product.update({
              where: { id },
              data: {
                tags: {
                  connect: { id: tag.id }
                }
              }
            });

            console.log(`Created relationship between product ${id} and tag ${tag.id}`);
          } catch (tagError) {
            console.error(`Error processing tag "${tagName}":`, tagError);

            // If tag creation fails due to duplicate slug, try to find the existing tag
            if (tagError.code === 'P2002') {
              const existingTag = await this.prisma.tag.findUnique({
                where: { name: tagName }
              });

              if (existingTag) {
                console.log(`Using existing tag after conflict: "${tagName}" with ID: ${existingTag.id}`);

                // Create the relationship with existing tag
                try {
                  await this.prisma.product.update({
                    where: { id },
                    data: {
                      tags: {
                        connect: { id: existingTag.id }
                      }
                    }
                  });
                  console.log(`Created relationship between product ${id} and existing tag ${existingTag.id}`);
                } catch (relationError) {
                  console.error(`Error creating relationship with existing tag:`, relationError);
                  if (relationError.code !== 'P2002') {
                    throw relationError;
                  }
                }
              }
            } else {
              throw tagError;
            }
          }
        }
      }

      console.log(`Finished processing tags for product ${id}`);
    }

    // Handle listings
    // Delete listings if specified
    if (deleteListingIds.length > 0) {
      await this.prisma.listing.deleteMany({
        where: {
          id: { in: deleteListingIds },
          productId: id,
        },
      });
    }

    // Add new listings if provided
    if (listings && listings.length > 0) {
      await this.prisma.listing.createMany({
        data: listings.map(listing => ({
          title: listing.title,
          content: listing.content,
          productId: id
        })),
        skipDuplicates: true,
      });
    }

    // 5) Return the updated product with all relationships
    return await this.prisma.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
      },
    });
  }






  async deleteProduct(id: number): Promise<Product> {
    try {
      // First check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
        },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      // Check if product is referenced in any orders
      const orderItemsCount = await this.prisma.orderItem.count({
        where: { productId: id }
      });

      if (orderItemsCount > 0) {
        throw new BadRequestException(
          `Cannot delete product. It is referenced in ${orderItemsCount} order(s). ` +
          `Products that have been ordered cannot be deleted to maintain order history integrity.`
        );
      }

      // Delete all relations in ProductCategories table
      await this.prisma.productCategories.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all relations in ProductTags table
      await this.prisma.productTags.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all product images
      await this.prisma.productImage.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all listings associated with the product
      await this.prisma.listing.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all product section items that reference this product
      await this.prisma.productSectionItem.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all product variants and their related data
      const variants = await this.prisma.productVariant.findMany({
        where: { productId: id },
        select: { id: true }
      });

      for (const variant of variants) {
        // Delete variant attribute values
        await this.prisma.variantAttributeValue.deleteMany({
          where: { variantId: variant.id },
        });
      }

      // Delete all product variants
      await this.prisma.productVariant.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all product attributes and their values
      const productAttributes = await this.prisma.productAttribute.findMany({
        where: { productId: id },
        select: { id: true }
      });

      for (const productAttribute of productAttributes) {
        // Delete product attribute values
        await this.prisma.productAttributeValue.deleteMany({
          where: { productAttributeId: productAttribute.id },
        });
      }

      // Delete product attributes
      await this.prisma.productAttribute.deleteMany({
        where: {
          productId: id,
        },
      });

      // Finally delete the product
      return await this.prisma.product.delete({
        where: { id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
          
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error deleting product:', error);
      throw error;
    }
  }
}



























