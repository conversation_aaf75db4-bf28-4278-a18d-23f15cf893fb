# Orders API Documentation

This document provides comprehensive documentation for the Orders API endpoints, including analytics and reporting capabilities.

## Base URL

All API endpoints are prefixed with:
```
http://localhost:3300/api/orders
```

## Authentication

All order endpoints require authentication and admin/super-admin role access.

**Headers Required:**
```
Authorization: Bearer <jwt_token>
```

## Endpoints Overview

### 1. Get All Orders (with Pagination and Filtering)
### 2. Get Order by ID
### 3. Order Analytics Endpoints
### 4. Order Reports and Insights

---

## 1. Get All Orders

Retrieve all orders with advanced filtering, pagination, and sorting capabilities.

**Endpoint:**
```
GET /api/orders
```

**Query Parameters:**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| page | number | No | 1 | Page number for pagination |
| limit | number | No | 30 | Number of items per page (max 100) |
| search | string | No | - | Search in order ID, customer name, or email |
| sortBy | enum | No | dateCreated_desc | Sort orders by specified field |
| status | enum | No | - | Filter by order status |
| dateFrom | string | No | - | Filter orders from date (ISO format) |
| dateTo | string | No | - | Filter orders to date (ISO format) |
| minAmount | number | No | - | Minimum order amount filter |
| maxAmount | number | No | - | Maximum order amount filter |
| customerId | number | No | - | Filter by specific customer ID |

**Sort Options (sortBy):**
- `dateCreated_desc` - Date created (newest first)
- `dateCreated_asc` - Date created (oldest first)
- `totalAmount_desc` - Total amount (highest first)
- `totalAmount_asc` - Total amount (lowest first)
- `status` - Order status
- `customerName` - Customer name

**Status Options:**
- `PENDING`
- `PROCESSING`
- `COMPLETED`
- `CANCELLED`
- `REFUNDED`

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders?page=1&limit=20&status=COMPLETED&dateFrom=2024-01-01&sortBy=totalAmount_desc" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "data": [
    {
      "id": 1,
      "customerId": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "companyName": null,
      "country": "USA",
      "streetAddress": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "phone": "+1234567890",
      "shippingName": "John Doe",
      "shippingEmail": "<EMAIL>",
      "shippingFirstName": "John",
      "shippingLastName": "Doe",
      "shippingCompany": null,
      "shippingCountry": "USA",
      "shippingStreet": "123 Main St",
      "shippingCity": "New York",
      "shippingState": "NY",
      "shippingZipCode": "10001",
      "shippingPhone": "+1234567890",
      "dateCreated": "2024-01-15T10:30:00.000Z",
      "couponId": null,
      "couponAmount": 0,
      "taxAmount": 8.50,
      "shippingAmount": 5.99,
      "shippingTaxAmount": 0.48,
      "totalAmount": 114.97,
      "status": "COMPLETED",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T11:00:00.000Z",
      "customer": {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "coupon": null,
      "items": [
        {
          "id": 1,
          "orderId": 1,
          "productId": 45,
          "variationId": null,
          "quantity": 2,
          "productNetRevenue": 50.00,
          "productGrossRevenue": 100.00,
          "itemName": null,
          "createdAt": "2024-01-15T10:30:00.000Z",
          "updatedAt": "2024-01-15T10:30:00.000Z",
          "product": {
            "id": 45,
            "name": "Premium Face Cream",
            "sku": "PFC-001"
          }
        }
      ]
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 100,
    "itemsPerPage": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "appliedFilters": {
    "page": 1,
    "limit": 20,
    "status": "COMPLETED",
    "dateFrom": "2024-01-01",
    "sortBy": "totalAmount_desc"
  }
}
```

---

## 2. Get Order by ID

Retrieve detailed information about a specific order.

**Endpoint:**
```
GET /api/orders/:id
```

**Path Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | number | Yes | Order ID |

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders/1" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "id": 1,
  "customerId": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "companyName": null,
  "country": "USA",
  "streetAddress": "123 Main St",
  "city": "New York",
  "state": "NY",
  "zipCode": "10001",
  "phone": "+1234567890",
  "shippingName": "John Doe",
  "shippingEmail": "<EMAIL>",
  "shippingFirstName": "John",
  "shippingLastName": "Doe",
  "shippingCompany": null,
  "shippingCountry": "USA",
  "shippingStreet": "123 Main St",
  "shippingCity": "New York",
  "shippingState": "NY",
  "shippingZipCode": "10001",
  "shippingPhone": "+1234567890",
  "dateCreated": "2024-01-15T10:30:00.000Z",
  "couponId": null,
  "couponAmount": 0,
  "taxAmount": 8.50,
  "shippingAmount": 5.99,
  "shippingTaxAmount": 0.48,
  "totalAmount": 114.97,
  "status": "COMPLETED",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T11:00:00.000Z",
  "customer": {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "coupon": null,
  "items": [
    {
      "id": 1,
      "orderId": 1,
      "productId": 45,
      "variationId": null,
      "quantity": 2,
      "productNetRevenue": 50.00,
      "productGrossRevenue": 100.00,
      "itemName": null,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "product": {
        "id": 45,
        "name": "Premium Face Cream",
        "sku": "PFC-001"
      }
    },
    {
      "id": 2,
      "orderId": 1,
      "productId": null,
      "variationId": null,
      "quantity": 1,
      "productNetRevenue": 0.00,
      "productGrossRevenue": 0.00,
      "itemName": "Legacy Product - Moisturizer",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "product": null
    }
  ]
}
```

**Note:** The response includes both modern orders (with `productId` and `product` details) and legacy migrated orders (with `itemName` only).

---

## 3. Order Analytics Endpoints

### 3.1 Last Day Analytics

Get order analytics for the last 24 hours with hourly breakdown.

**Endpoint:**
```
GET /api/orders/analytics/last-day
```

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders/analytics/last-day" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "totalOrders": 15,
  "totalRevenue": 1250.75,
  "averageOrderValue": 83.38,
  "completedOrders": 12,
  "pendingOrders": 2,
  "cancelledOrders": 1,
  "refundedOrders": 0,
  "processingOrders": 0,
  "dailyStats": [
    {
      "date": "2024-01-15",
      "totalOrders": 15,
      "totalRevenue": 1250.75,
      "averageOrderValue": 83.38,
      "completedOrders": 12
    }
  ]
}
```

### 3.2 Last Month Analytics

Get order analytics for the last 30 days with daily breakdown.

**Endpoint:**
```
GET /api/orders/analytics/last-month
```

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders/analytics/last-month" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "totalOrders": 450,
  "totalRevenue": 37500.25,
  "averageOrderValue": 83.33,
  "completedOrders": 380,
  "pendingOrders": 45,
  "cancelledOrders": 20,
  "refundedOrders": 5,
  "processingOrders": 0,
  "dailyStats": [
    {
      "date": "2024-01-01",
      "totalOrders": 12,
      "totalRevenue": 980.50,
      "averageOrderValue": 81.71,
      "completedOrders": 10
    },
    {
      "date": "2024-01-02",
      "totalOrders": 18,
      "totalRevenue": 1520.75,
      "averageOrderValue": 84.49,
      "completedOrders": 15
    }
  ]
}
```

### 3.3 Last Year Analytics

Get order analytics for the last 12 months with monthly breakdown.

**Endpoint:**
```
GET /api/orders/analytics/last-year
```

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders/analytics/last-year" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "totalOrders": 5400,
  "totalRevenue": 450000.00,
  "averageOrderValue": 83.33,
  "completedOrders": 4560,
  "pendingOrders": 540,
  "cancelledOrders": 240,
  "refundedOrders": 60,
  "processingOrders": 0,
  "monthlyStats": [
    {
      "year": 2024,
      "month": "2024-01",
      "totalOrders": 450,
      "totalRevenue": 37500.00,
      "averageOrderValue": 83.33,
      "completedOrders": 380
    },
    {
      "year": 2024,
      "month": "2024-02",
      "totalOrders": 520,
      "totalRevenue": 43200.00,
      "averageOrderValue": 83.08,
      "completedOrders": 440
    }
  ]
}
```

### 3.4 Last 5 Years Analytics

Get order analytics for the last 5 years with yearly breakdown.

**Endpoint:**
```
GET /api/orders/analytics/last-5-years
```

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders/analytics/last-5-years" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "totalOrders": 27000,
  "totalRevenue": 2250000.00,
  "averageOrderValue": 83.33,
  "completedOrders": 22800,
  "pendingOrders": 2700,
  "cancelledOrders": 1200,
  "refundedOrders": 300,
  "processingOrders": 0,
  "yearlyStats": [
    {
      "year": 2020,
      "totalOrders": 4800,
      "totalRevenue": 400000.00,
      "averageOrderValue": 83.33,
      "completedOrders": 4080
    },
    {
      "year": 2021,
      "totalOrders": 5200,
      "totalRevenue": 433000.00,
      "averageOrderValue": 83.27,
      "completedOrders": 4420
    },
    {
      "year": 2022,
      "totalOrders": 5600,
      "totalRevenue": 467000.00,
      "averageOrderValue": 83.39,
      "completedOrders": 4760
    },
    {
      "year": 2023,
      "totalOrders": 6000,
      "totalRevenue": 500000.00,
      "averageOrderValue": 83.33,
      "completedOrders": 5100
    },
    {
      "year": 2024,
      "totalOrders": 5400,
      "totalRevenue": 450000.00,
      "averageOrderValue": 83.33,
      "completedOrders": 4560
    }
  ]
}
```

---

## 4. Order Reports and Insights

### 4.1 Complete Order Reports

Get comprehensive analytics for all time periods in a single request.

**Endpoint:**
```
GET /api/orders/reports
```

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders/reports" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "lastDay": {
    "totalOrders": 15,
    "totalRevenue": 1250.75,
    "averageOrderValue": 83.38,
    "completedOrders": 12,
    "pendingOrders": 2,
    "cancelledOrders": 1,
    "refundedOrders": 0,
    "processingOrders": 0,
    "dailyStats": [...]
  },
  "lastMonth": {
    "totalOrders": 450,
    "totalRevenue": 37500.25,
    "averageOrderValue": 83.33,
    "completedOrders": 380,
    "pendingOrders": 45,
    "cancelledOrders": 20,
    "refundedOrders": 5,
    "processingOrders": 0,
    "dailyStats": [...]
  },
  "lastYear": {
    "totalOrders": 5400,
    "totalRevenue": 450000.00,
    "averageOrderValue": 83.33,
    "completedOrders": 4560,
    "pendingOrders": 540,
    "cancelledOrders": 240,
    "refundedOrders": 60,
    "processingOrders": 0,
    "monthlyStats": [...]
  },
  "last5Years": {
    "totalOrders": 27000,
    "totalRevenue": 2250000.00,
    "averageOrderValue": 83.33,
    "completedOrders": 22800,
    "pendingOrders": 2700,
    "cancelledOrders": 1200,
    "refundedOrders": 300,
    "processingOrders": 0,
    "yearlyStats": [...]
  }
}
```

### 4.2 Order Insights with Top Products and Customers

Get detailed insights including top products, customers, and revenue breakdown.

**Endpoint:**
```
GET /api/orders/insights
```

**Query Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| dateFrom | string | No | Start date for insights (ISO format) |
| dateTo | string | No | End date for insights (ISO format) |

**Example Request:**
```bash
curl -X GET "http://localhost:3300/api/orders/insights?dateFrom=2024-01-01&dateTo=2024-12-31" \
  -H "Authorization: Bearer your_jwt_token"
```

**Example Response:**
```json
{
  "analytics": {
    "totalOrders": 5400,
    "totalRevenue": 450000.00,
    "averageOrderValue": 83.33,
    "completedOrders": 4560,
    "pendingOrders": 540,
    "cancelledOrders": 240,
    "refundedOrders": 60,
    "processingOrders": 0
  },
  "topProducts": [
    {
      "productId": 45,
      "productName": "Premium Face Cream",
      "totalQuantity": 1200,
      "totalRevenue": 60000.00,
      "orderCount": 800
    },
    {
      "productId": null,
      "productName": "Legacy Product - Moisturizer",
      "totalQuantity": 800,
      "totalRevenue": 32000.00,
      "orderCount": 400
    }
  ],
  "topCustomers": [
    {
      "customerId": 123,
      "customerName": "John Doe",
      "customerEmail": "<EMAIL>",
      "totalOrders": 25,
      "totalSpent": 2500.00,
      "averageOrderValue": 100.00,
      "lastOrderDate": "2024-01-15T10:30:00.000Z"
    },
    {
      "customerId": null,
      "customerName": "Guest Customer",
      "customerEmail": "<EMAIL>",
      "totalOrders": 1,
      "totalSpent": 85.00,
      "averageOrderValue": 85.00,
      "lastOrderDate": "2024-01-10T14:20:00.000Z"
    }
  ],
  "revenueByStatus": [
    {
      "status": "COMPLETED",
      "count": 4560,
      "revenue": 380000.00
    },
    {
      "status": "PENDING",
      "count": 540,
      "revenue": 45000.00
    },
    {
      "status": "PROCESSING",
      "count": 0,
      "revenue": 0.00
    },
    {
      "status": "CANCELLED",
      "count": 240,
      "revenue": 20000.00
    },
    {
      "status": "REFUNDED",
      "count": 60,
      "revenue": 5000.00
    }
  ]
}
```

---

## Error Responses

All endpoints may return the following error responses:

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Unauthorized"
}
```

### 403 Forbidden
```json
{
  "statusCode": 403,
  "message": "Forbidden resource"
}
```

### 404 Not Found (for specific order)
```json
{
  "statusCode": 404,
  "message": "Order with ID 999 not found"
}
```

### 400 Bad Request (validation errors)
```json
{
  "statusCode": 400,
  "message": [
    "page must be a positive number",
    "limit must not be greater than 100"
  ],
  "error": "Bad Request"
}
```

---

## Data Types and Important Notes

### Order Types
The system handles two types of orders:

1. **Modern Orders**: Created through the current system
   - Have `productId` references
   - Include full product details
   - Use current pricing structure

2. **Legacy Orders**: Migrated from previous system
   - May have `itemName` instead of `productId`
   - Limited product information
   - Preserved for historical data

### Date Fields
- `dateCreated`: The actual creation date of the order (used for filtering and analytics)
- `createdAt`: System timestamp (may differ from dateCreated for migrated orders)
- `updatedAt`: Last modification timestamp

### Currency
All monetary values are returned as numbers representing the amount in the base currency (e.g., USD).

### Pagination
- Default page size is 30 items
- Maximum page size is 100 items
- Page numbers start from 1

### Performance Considerations
- Analytics endpoints may take longer for large date ranges
- Use date filters to improve performance
- Consider caching results for frequently accessed reports
