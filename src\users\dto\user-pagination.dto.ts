import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class UserPaginationQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Page must be a number' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Limit must be a number' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must not exceed 100' })
  limit?: number = 30;

  @IsOptional()
  @IsString({ message: 'Search must be a string' })
  search?: string;
}

export class UserPaginationDto {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export class UserListItemDto {
  id: number;
  name: string;
  email: string;
  firstName?: string;
  lastName?: string;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  isVerified: boolean;
  isBanned: boolean;
  createdAt: Date;
  lastLogin?: Date;
  points: number;
  city?: string;
  country?: string;
  companyName?: string;
}

export class PaginatedUsersResponseDto {
  pagination: UserPaginationDto;
  data: UserListItemDto[];
  searchTerm?: string;
}
