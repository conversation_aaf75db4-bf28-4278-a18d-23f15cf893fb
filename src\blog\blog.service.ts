import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import { BlogResponseDto, BlogListResponseDto, PaginatedBlogsResponseDto } from './dto/blog-response.dto';

@Injectable()
export class BlogService {
  constructor(private prisma: PrismaService) {}

  async create(createBlogDto: CreateBlogDto): Promise<BlogResponseDto> {
    const { author, seo, ...blogData } = createBlogDto;

    // Check if slug already exists
    const existingBlog = await this.prisma.blog.findUnique({
      where: { slug: blogData.slug },
    });

    if (existingBlog) {
      throw new ConflictException(`Blog with slug "${blogData.slug}" already exists`);
    }

    // Prepare the data for creation
    const data: any = {
      ...blogData,
      content: blogData.content || [],
      tags: blogData.tags || [],
      featured: blogData.featured || false,
      published: blogData.published || false,
    };

    // Handle author data
    data.authorName = author.name;
    data.authorAvatar = author.avatar;
    data.authorBio = author.bio;

    // Handle SEO data
    if (seo) {
      data.metaTitle = seo.metaTitle;
      data.metaDescription = seo.metaDescription;
      data.keywords = seo.keywords || [];
    }

    const blog = await this.prisma.blog.create({
      data,
    });

    return this.formatBlogResponse(blog);
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    published?: boolean,
    featured?: boolean,
    tags?: string[]
  ): Promise<PaginatedBlogsResponseDto> {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    if (published !== undefined) {
      where.published = published;
    }
    
    if (featured !== undefined) {
      where.featured = featured;
    }
    
    if (tags && tags.length > 0) {
      where.tags = {
        hasSome: tags,
      };
    }

    const [blogs, total] = await Promise.all([
      this.prisma.blog.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdDate: 'desc' },
      }),
      this.prisma.blog.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: blogs.map(blog => this.formatBlogListResponse(blog)),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: number): Promise<BlogResponseDto> {
    const blog = await this.prisma.blog.findUnique({
      where: { id },
    });

    if (!blog) {
      throw new NotFoundException(`Blog with ID ${id} not found`);
    }

    return this.formatBlogResponse(blog);
  }

  async findBySlug(slug: string): Promise<BlogResponseDto> {
    const blog = await this.prisma.blog.findUnique({
      where: { slug },
    });

    if (!blog) {
      throw new NotFoundException(`Blog with slug "${slug}" not found`);
    }

    if (!blog.published) {
      throw new NotFoundException(`Blog with slug "${slug}" not found`);
    }

    return this.formatBlogResponse(blog);
  }

  async update(id: number, updateBlogDto: UpdateBlogDto): Promise<BlogResponseDto> {
    const existingBlog = await this.prisma.blog.findUnique({
      where: { id },
    });

    if (!existingBlog) {
      throw new NotFoundException(`Blog with ID ${id} not found`);
    }

    // Check if slug already exists (if being updated)
    if (updateBlogDto.slug && updateBlogDto.slug !== existingBlog.slug) {
      const blogWithSlug = await this.prisma.blog.findUnique({
        where: { slug: updateBlogDto.slug },
      });

      if (blogWithSlug) {
        throw new ConflictException(`Blog with slug "${updateBlogDto.slug}" already exists`);
      }
    }

    const { author, seo, ...blogData } = updateBlogDto;

    // Prepare the data for update
    const data: any = { ...blogData };

    // Handle author data
    if (author) {
      data.authorName = author.name;
      data.authorAvatar = author.avatar;
      data.authorBio = author.bio;
    }

    // Handle SEO data
    if (seo) {
      data.metaTitle = seo.metaTitle;
      data.metaDescription = seo.metaDescription;
      data.keywords = seo.keywords;
    }

    const blog = await this.prisma.blog.update({
      where: { id },
      data,
    });

    return this.formatBlogResponse(blog);
  }

  async remove(id: number): Promise<BlogResponseDto> {
    const blog = await this.prisma.blog.findUnique({
      where: { id },
    });

    if (!blog) {
      throw new NotFoundException(`Blog with ID ${id} not found`);
    }

    await this.prisma.blog.delete({
      where: { id },
    });

    return this.formatBlogResponse(blog);
  }

  async getAllTags(): Promise<string[]> {
    const blogs = await this.prisma.blog.findMany({
      where: { published: true },
      select: { tags: true },
    });

    const allTags = new Set<string>();
    blogs.forEach(blog => {
      blog.tags.forEach(tag => allTags.add(tag));
    });

    return Array.from(allTags).sort();
  }

  async getFeaturedBlogs(): Promise<BlogListResponseDto[]> {
    const blogs = await this.prisma.blog.findMany({
      where: {
        featured: true,
        published: true
      },
      orderBy: { createdDate: 'desc' },
    });

    return blogs.map(blog => this.formatBlogListResponse(blog));
  }

  private formatBlogResponse(blog: any): BlogResponseDto {
    return {
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      tags: blog.tags || [],
      content: blog.content || [],
      blogImage: blog.blogImage,
      author: {
        name: blog.authorName,
        avatar: blog.authorAvatar,
        bio: blog.authorBio,
      },
      createdDate: blog.createdDate,
      excerpt: blog.excerpt,
      readTime: blog.readTime,
      featured: blog.featured,
      published: blog.published,
      seo: {
        metaTitle: blog.metaTitle,
        metaDescription: blog.metaDescription,
        keywords: blog.keywords || [],
      },
      createdAt: blog.createdAt,
      updatedAt: blog.updatedAt,
    };
  }

  private formatBlogListResponse(blog: any): BlogListResponseDto {
    return {
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      tags: blog.tags || [],
      blogImage: blog.blogImage,
      author: {
        name: blog.authorName,
        avatar: blog.authorAvatar,
        bio: blog.authorBio,
      },
      createdDate: blog.createdDate,
      excerpt: blog.excerpt,
      readTime: blog.readTime,
      featured: blog.featured,
      published: blog.published,
      createdAt: blog.createdAt,
      updatedAt: blog.updatedAt,
    };
  }
}
