export class OrderAnalyticsDto {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  completedOrders: number;
  pendingOrders: number;
  cancelledOrders: number;
  refundedOrders: number;
  processingOrders: number;
}

export class DailyOrderStatsDto {
  date: string;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  completedOrders: number;
}

export class MonthlyOrderStatsDto {
  month: string;
  year: number;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  completedOrders: number;
}

export class YearlyOrderStatsDto {
  year: number;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  completedOrders: number;
}

export class OrderReportsDto {
  lastDay: OrderAnalyticsDto & { dailyStats: DailyOrderStatsDto[] };
  lastMonth: OrderAnalyticsDto & { dailyStats: DailyOrderStatsDto[] };
  lastYear: OrderAnalyticsDto & { monthlyStats: MonthlyOrderStatsDto[] };
  last5Years: OrderAnalyticsDto & { yearlyStats: YearlyOrderStatsDto[] };
}

export class TopProductsDto {
  productId?: number;
  productName: string;
  totalQuantity: number;
  totalRevenue: number;
  orderCount: number;
}

export class CustomerInsightsDto {
  customerId?: number;
  customerName: string;
  customerEmail: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate: Date;
}

export class OrderInsightsDto {
  analytics: OrderAnalyticsDto;
  topProducts: TopProductsDto[];
  topCustomers: CustomerInsightsDto[];
  revenueByStatus: {
    status: string;
    count: number;
    revenue: number;
  }[];
}
