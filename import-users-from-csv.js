const fs = require('fs');
const csv = require('csv-parser');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

// Function to parse the date format from Excel (4/29/2025  7:15:41 PM)
function parseExcelDate(dateString) {
  if (!dateString || dateString.trim() === '') {
    return null;
  }
  
  try {
    // Handle the format: "4/29/2025  7:15:41 PM"
    const cleanedDate = dateString.trim();
    const date = new Date(cleanedDate);
    
    if (isNaN(date.getTime())) {
      console.log(`Invalid date format: ${dateString}`);
      return null;
    }
    
    return date;
  } catch (error) {
    console.log(`Error parsing date: ${dateString}`, error.message);
    return null;
  }
}

// Function to split display name into first and last name
function splitDisplayName(displayName) {
  if (!displayName || displayName.trim() === '') {
    return { firstName: null, lastName: null };
  }
  
  const nameParts = displayName.trim().split(' ');
  
  if (nameParts.length === 1) {
    return { firstName: nameParts[0], lastName: null };
  } else {
    return { 
      firstName: nameParts[0], 
      lastName: nameParts.slice(1).join(' ') 
    };
  }
}

// Function to parse CSV manually
function parseCSV(csvContent) {
  const lines = csvContent.split('\n');
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  const rows = [];

  for (let i = 1; i < lines.length; i++) {
    if (lines[i].trim() === '') continue;

    const values = [];
    let current = '';
    let inQuotes = false;

    for (let j = 0; j < lines[i].length; j++) {
      const char = lines[i][j];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    values.push(current.trim());

    const row = {};
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });
    rows.push(row);
  }

  return rows;
}

async function importUsers() {
  console.log('🚀 Starting user import from D1Dka_users.csv...');

  const users = [];
  const errors = [];
  let skippedCount = 0;
  let processedCount = 0;

  try {
    // Read CSV file
    const csvContent = fs.readFileSync('D1Dka_users.csv', 'utf8');
    const rows = parseCSV(csvContent);

    console.log(`📊 Found ${rows.length} rows in CSV file`);

    // Process each row
    for (const row of rows) {
      try {
        processedCount++;

        // Skip user with ID 1 as requested
        if (parseInt(row.ID) === 1) {
          console.log(`⏭️  Skipping user with ID 1 as requested`);
          skippedCount++;
          continue;
        }

        // Parse and validate required fields
        const userId = parseInt(row.ID);
        const username = row.user_login?.trim();
        const email = row.user_email?.trim();
        const displayName = row.display_name?.trim();
        const userRegistered = parseExcelDate(row.user_registered);

        // Validate required fields
        if (!userId || !username || !email) {
          errors.push(`Row ${processedCount}: Missing required fields (ID: ${userId}, username: ${username}, email: ${email})`);
          continue;
        }

        // Split display name
        const { firstName, lastName } = splitDisplayName(displayName);

        // Create user object
        const user = {
          id: userId,
          name: username, // user_login goes to name field
          email: email,
          firstName: firstName,
          lastName: lastName,
          lastLogin: userRegistered, // user_registered goes to lastLogin
          password: 'PLACEHOLDER_PASSWORD_RESET_REQUIRED', // Placeholder as requested
          isVerified: false, // Default to false, they'll need to reset password
          createdAt: userRegistered || new Date(), // Use registration date or current date
        };

        users.push(user);

      } catch (error) {
        errors.push(`Row ${processedCount}: Error processing row - ${error.message}`);
      }
    }

    // Process results
    console.log(`📊 CSV parsing complete:`);
    console.log(`   - Total rows processed: ${processedCount}`);
    console.log(`   - Users to import: ${users.length}`);
    console.log(`   - Skipped: ${skippedCount}`);
    console.log(`   - Errors: ${errors.length}`);

    if (errors.length > 0) {
      console.log('\n❌ Errors found:');
      errors.forEach(error => console.log(`   ${error}`));
    }

    if (users.length === 0) {
      console.log('❌ No valid users to import');
      return;
    }

    // Hash the placeholder password
    const hashedPassword = await bcrypt.hash('PLACEHOLDER_PASSWORD_RESET_REQUIRED', 10);

    // Import users to database
    console.log('\n💾 Starting database import...');
    let successCount = 0;
    let failCount = 0;
        
        for (const user of users) {
          try {
            // Check if user with this ID already exists
            const existingUser = await prisma.user.findUnique({
              where: { id: user.id }
            });
            
            if (existingUser) {
              console.log(`⚠️  User with ID ${user.id} already exists, skipping`);
              skippedCount++;
              continue;
            }
            
            // Check if user with this email already exists
            const existingEmailUser = await prisma.user.findUnique({
              where: { email: user.email }
            });
            
            if (existingEmailUser) {
              console.log(`⚠️  User with email ${user.email} already exists, skipping`);
              skippedCount++;
              continue;
            }
            
            // Create user
            await prisma.user.create({
              data: {
                ...user,
                password: hashedPassword
              }
            });
            
            successCount++;
            console.log(`✅ Created user: ${user.name} (${user.email})`);
            
          } catch (error) {
            failCount++;
            console.log(`❌ Failed to create user ${user.name}: ${error.message}`);
          }
        }
        
        console.log(`\n🎉 Import complete:`);
        console.log(`   - Successfully imported: ${successCount}`);
        console.log(`   - Failed: ${failCount}`);
        console.log(`   - Skipped: ${skippedCount}`);
        console.log(`   - Total processed: ${processedCount}`);
        
        resolve();
      })
      .on('error', (error) => {
        console.error('❌ Error reading CSV file:', error);
        reject(error);
      });
  });
}

// Run the import
async function main() {
  try {
    await importUsers();
  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
