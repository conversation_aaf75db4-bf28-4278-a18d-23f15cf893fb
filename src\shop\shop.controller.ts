import { Controller, Get, Param, ParseIntPipe, Query, NotFoundException } from '@nestjs/common';
import { AccessLevel } from '@prisma/client';
import { ShopService } from './shop.service';
import { MainCategoryDto } from './dto/main-category.dto';
import { SubcategoriesDto } from './dto/subcategories.dto';
import { PaginatedProductsDto } from './dto/paginated-products.dto';
import { ProductDetailsDto } from './dto/product-details.dto';
import { EnhancedPaginatedProductsDto } from './dto/paginated-products.dto';
import { ProductFiltersDto } from './dto/product-filters.dto';
import { CategoryTreeResponseDto } from './dto/category-tree.dto';


@Controller('shop')
export class ShopController {
  constructor(private readonly shopService: ShopService) {}

  @Get('main-categories')
  getMainCategories(): Promise<{ data: MainCategoryDto[] }> {
    return this.shopService.getMainCategories();
  }

  @Get('main-categories/:mainId/categories')
  getSubcategories(
    @Param('mainId', ParseIntPipe) mainId: number,
  ): Promise<SubcategoriesDto> {
    return this.shopService.getSubcategories(mainId);
  }

  @Get('categories/tree')
  getCategoryTree(): Promise<CategoryTreeResponseDto> {
    return this.shopService.getCategoryTree();
  }

  @Get('categories/:catId/products')
  getProductsByCategory(
    @Param('catId', ParseIntPipe) catId: number,
    @Query() filters: ProductFiltersDto,
  ): Promise<PaginatedProductsDto> {
    return this.shopService.getProductsByCategory(catId, filters);
  }

  @Get('categories/:catId/filters')
  getCategoryFilters(
    @Param('catId', ParseIntPipe) catId: number,
  ) {
    return this.shopService.getFilterSummary(catId);
  }


  @Get('products')
  getAllProducts(
    @Query() filters: ProductFiltersDto,
  ): Promise<PaginatedProductsDto> {
    return this.shopService.getAllProducts(filters);
  }

  @Get('products/names')
  getAllProductNames(): Promise<{ data: { id: number; name: string; slug: string; access: AccessLevel }[] }> {
    return this.shopService.getAllProductNames();
  }

  @Get('products/filters')
  getProductFilters() {
    return this.shopService.getFilterSummary();
  }

  @Get('products/slug/:slug')
  getProductDetailsBySlug(
    @Param('slug') slug: string,
    @Query('password') password?: string,
  ): Promise<ProductDetailsDto> {
    return this.shopService.getProductDetailsBySlug(slug, password);
  }

  // @Get('products/:id')
  // getProductDetails(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Query('password') password?: string,
  // ): Promise<ProductDetailsDto> {
  //   return this.shopService.getProductDetails(id, password);
  // }

  @Get('products/search')
  async search(
    @Query('query') query: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    if (!query?.trim()) return [];
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 20;
    return this.shopService.searchProducts(query.trim(),  limitNumber,pageNumber);
  }

  @Get('wholesale')
  getWholesaleProducts(
    @Query() filters: ProductFiltersDto,
  ): Promise<EnhancedPaginatedProductsDto> {
    return this.shopService.getWholesaleProducts(filters);
  }

  @Get('wholesale/filters')
  getWholesaleFilters() {
    return this.shopService.getWholesaleFilterSummary();
  }

  @Get(':mainCategorySlug/:categorySlug')
  getProductsByMainCategoryAndCategorySlug(
    @Param('mainCategorySlug') mainCategorySlug: string,
    @Param('categorySlug') categorySlug: string,
    @Query() filters: ProductFiltersDto,
  ): Promise<EnhancedPaginatedProductsDto> {
    return this.shopService.getProductsByMainCategoryAndCategorySlug(mainCategorySlug, categorySlug, filters);
  }

  @Get(':mainCategorySlug/filters')
  getMainCategoryFilters(
    @Param('mainCategorySlug') mainCategorySlug: string,
  ) {
    return this.shopService.getMainCategoryFilterSummary(mainCategorySlug);
  }

  @Get(':mainCategorySlug')
  getProductsByMainCategorySlug(
    @Param('mainCategorySlug') mainCategorySlug: string,
    @Query() filters: ProductFiltersDto,
  ): Promise<EnhancedPaginatedProductsDto> {
    return this.shopService.getProductsByMainCategorySlug(mainCategorySlug, filters);
  }

}