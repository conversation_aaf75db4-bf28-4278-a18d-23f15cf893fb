generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearchPostgres", "postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("Production_DB_EXTERNAL")
  extensions = [pg_trgm]
}

model User {
  id            Int       @id @default(autoincrement())
  name          String
  email         String    @unique
  bannedById    Int?
  bannedReason  String?
  bannedUntil   DateTime?
  city          String?
  companyName   String?
  country       String?
  createdAt     DateTime  @default(now())
  firstName     String?
  isAdmin       <PERSON>   @default(false)
  isBanned      Boolean   @default(false)
  isSuperAd<PERSON>   @default(false)
  isVerified    Boolean   @default(false)
  lastName      String?
  lastLogin     DateTime?
  password      String
  phone         String?
  points        Int       @default(0)
  state         String?
  streetAddress String?
  updatedAt     DateTime  @updatedAt
  zipCode       String?
  orders        Order[]
  bannedBy      User?     @relation("BannedBy", fields: [bannedById], references: [id])
  bannedUsers   User[]    @relation("BannedBy")
}

model Product {
  id                  Int                      @id @default(autoincrement())
  createdAt           DateTime                 @default(now())
  updatedAt           DateTime                 @updatedAt
  sku                 String                   @unique
  name                String
  description         String?
  shortDescription    String?
  price               Decimal                  @db.Decimal(10, 2)
  salePrice           Decimal?                 @db.Decimal(10, 2)
  saleStart           DateTime?
  saleEnd             DateTime?
  stockQuantity       Int?
  stockStatus         StockStatus              @default(IN_STOCK)
  taxStatus           TaxStatus                @default(TAXABLE)
  taxClass            TaxClass                 @default(STANDARD)
  type                ProductType              @default(SIMPLE)
  access              AccessLevel              @default(PUBLIC)
  password            String?
  slug                String                   @unique
  search_vector       Unsupported("tsvector")? @default(dbgenerated("((setweight(to_tsvector('english'::regconfig, COALESCE(name, ''::text)), 'A'::\"char\") || setweight(to_tsvector('english'::regconfig, COALESCE(\"shortDescription\", ''::text)), 'B'::\"char\")) || setweight(to_tsvector('english'::regconfig, COALESCE(description, ''::text)), 'C'::\"char\"))"))
  listings            Listing[]
  orderItems          OrderItem[]
  ProductAttribute    ProductAttribute[]
  ProductCategories   ProductCategories[]
  images              ProductImage[]
  ProductSectionItems ProductSectionItem[]
  ProductTags         ProductTags[]
  variants            ProductVariant[]
  categories          Category[]               @relation("ProductCategories")
  tags                Tag[]                    @relation("ProductTags")

  @@index([name(ops: raw("gin_trgm_ops"))], map: "product_name_trgm_idx", type: Gin)
  @@index([search_vector], map: "product_search_vector_idx", type: Gin)
}

model ProductImage {
  id        Int             @id @default(autoincrement())
  url       String
  position  Int             @default(0)
  productId Int?
  variantId Int?
  product   Product?        @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])
}

model ProductVariant {
  id            Int                     @id @default(autoincrement())
  sku           String                  @unique
  price         Decimal                 @db.Decimal(10, 2)
  salePrice     Decimal?                @db.Decimal(10, 2)
  saleStart     DateTime?
  saleEnd       DateTime?
  stockQuantity Int?
  stockStatus   StockStatus             @default(IN_STOCK)
  productId     Int
  orderItems    OrderItem[]
  ProductImage  ProductImage[]
  product       Product                 @relation(fields: [productId], references: [id])
  attributes    VariantAttributeValue[]
}

model Attribute {
  id               Int                @id @default(autoincrement())
  name             String             @unique
  ProductAttribute ProductAttribute[]
}

/// pivot table tying a Product → an Attribute → its set of values
model ProductAttribute {
  id          Int                     @id @default(autoincrement())
  productId   Int
  attributeId Int
  attribute   Attribute               @relation(fields: [attributeId], references: [id])
  product     Product                 @relation(fields: [productId], references: [id])
  values      ProductAttributeValue[]
}

model ProductAttributeValue {
  id                 Int                     @id @default(autoincrement())
  productAttributeId Int
  /// e.g. "M", "L" or "Red", "Yellow"
  value              String
  productAttribute   ProductAttribute        @relation(fields: [productAttributeId], references: [id])
  assignments        VariantAttributeValue[]
}

model VariantAttributeValue {
  variantId Int
  valueId   Int
  value     ProductAttributeValue @relation(fields: [valueId], references: [id])
  variant   ProductVariant        @relation(fields: [variantId], references: [id])

  @@id([variantId, valueId])
}

model MainCategory {
  id         Int        @id @default(autoincrement())
  name       String     @unique
  slug       String     @unique
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  imageUrl   String     @default("https://placehold.co/600x400?text=Catgeory")
  categories Category[]
}

model Category {
  id                Int                 @id @default(autoincrement())
  name              String              @unique
  slug              String              @unique
  description       String?
  mainCategoryId    Int?
  imageUrl          String              @default("https://placehold.co/600x400?text=Catgeory")
  mainCategory      MainCategory?       @relation(fields: [mainCategoryId], references: [id])
  ProductCategories ProductCategories[]
  products          Product[]           @relation("ProductCategories")
}

model Tag {
  id          Int           @id @default(autoincrement())
  name        String        @unique
  slug        String        @unique
  ProductTags ProductTags[]
  products    Product[]     @relation("ProductTags")
}

model ProductCategories {
  productId  Int
  categoryId Int
  category   Category @relation(fields: [categoryId], references: [id])
  product    Product  @relation(fields: [productId], references: [id])

  @@id([productId, categoryId])
}

model ProductTags {
  productId Int
  tagId     Int
  product   Product @relation(fields: [productId], references: [id])
  tag       Tag     @relation(fields: [tagId], references: [id])

  @@id([productId, tagId])
}

model Listing {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  title     String
  content   String
  productId Int
  product   Product  @relation(fields: [productId], references: [id])
}

model ProductSection {
  id        Int                  @id @default(autoincrement())
  name      String
  position  Int                  @unique
  createdAt DateTime             @default(now())
  updatedAt DateTime             @updatedAt
  items     ProductSectionItem[]
}

model ProductSectionItem {
  id               Int            @id @default(autoincrement())
  productId        Int
  productSectionId Int
  position         Int
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  product          Product        @relation(fields: [productId], references: [id])
  productSection   ProductSection @relation(fields: [productSectionId], references: [id])

  @@unique([productSectionId, position])
}

model Order {
  id                Int         @id @default(autoincrement())
  customerId        Int?
  name              String
  email             String
  firstName         String?
  lastName          String?
  companyName       String?
  country           String?
  streetAddress     String?
  city              String?
  state             String?
  zipCode           String?
  phone             String?
  shippingName      String?
  shippingEmail     String?
  shippingFirstName String?
  shippingLastName  String?
  shippingCompany   String?
  shippingCountry   String?
  shippingStreet    String?
  shippingCity      String?
  shippingState     String?
  shippingZipCode   String?
  shippingPhone     String?
  dateCreated       DateTime    @default(now())
  couponId          Int?
  couponAmount      Decimal     @default(0) @db.Decimal(10, 2)
  taxAmount         Decimal     @default(0) @db.Decimal(10, 2)
  shippingAmount    Decimal     @default(0) @db.Decimal(10, 2)
  shippingTaxAmount Decimal     @default(0) @db.Decimal(10, 2)
  totalAmount       Decimal     @db.Decimal(10, 2)
  status            OrderStatus @default(PENDING)
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  coupon            Coupon?     @relation(fields: [couponId], references: [id])
  customer          User?       @relation(fields: [customerId], references: [id])
  items             OrderItem[]
}

model Coupon {
  id             Int        @id @default(autoincrement())
  code           String     @unique
  description    String?
  type           CouponType
  value          Decimal    @db.Decimal(10, 2)
  minOrderAmount Decimal?   @db.Decimal(10, 2)
  maxDiscount    Decimal?   @db.Decimal(10, 2)
  startDate      DateTime?
  endDate        DateTime?
  usageLimit     Int?
  usageCount     Int        @default(0)
  isActive       Boolean    @default(true)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  orders         Order[]
}

model OrderItem {
  id                  Int             @id @default(autoincrement())
  orderId             Int
  productId           Int?            // Made optional for legacy data
  variationId         Int?
  quantity            Int
  productNetRevenue   Decimal         @db.Decimal(10, 2)
  productGrossRevenue Decimal         @db.Decimal(10, 2)
  itemName            String?         // For legacy items without product mapping
  itemType            String?         // For legacy item type (line_item, etc.)
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @updatedAt
  order               Order           @relation(fields: [orderId], references: [id])
  product             Product?        @relation(fields: [productId], references: [id])
  variation           ProductVariant? @relation(fields: [variationId], references: [id])
}

model TaxSettings {
  id        Int      @id @default(autoincrement())
  name      String   @default("Default Tax")
  value     Decimal  @db.Decimal(10, 2)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Blog {
  id              Int      @id @default(autoincrement())
  title           String
  slug            String   @unique
  tags            String[]
  content         Json
  blogImage       String?
  authorName      String
  authorAvatar    String?
  authorBio       String?
  createdDate     DateTime @default(now())
  excerpt         String?
  readTime        Int?
  featured        Boolean  @default(false)
  published       Boolean  @default(false)
  metaTitle       String?
  metaDescription String?
  keywords        String[]
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

enum StockStatus {
  IN_STOCK
  OUT_OF_STOCK
  ON_BACKORDER
}

enum TaxStatus {
  TAXABLE
  SHIPPING
  NONE
}

enum TaxClass {
  STANDARD
  REDUCED
  ZERO
}

enum ProductType {
  SIMPLE
  VARIABLE
  GROUPED
  EXTERNAL
}

enum AccessLevel {
  PUBLIC
  PRIVATE
  PROTECTED
}

enum CouponType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum OrderStatus {
  PENDING
  PROCESSING
  COMPLETED
  CANCELLED
  REFUNDED
}
