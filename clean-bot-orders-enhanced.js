const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['error'],
  errorFormat: 'minimal'
});

// Configuration
const DRY_RUN = false; // Set to false to actually delete data
const FOCUS_MONTHS = ['2024-10', '2024-12']; // Focus on these months for analysis
const ANALYZE_ALL_MONTHS = false; // Set to true to analyze all months

// Thresholds for different detection strategies
const THRESHOLDS = {
  EXACT_DUPLICATE: 5,      // Same customer, same day, same items
  CUSTOMER_SPAM: 20,       // Same customer, many orders per day
  PRODUCT_SPAM: 10,        // Same product, many times per day (any customer)
  CRITICAL_SPAM: 50        // Extremely high volume
};

// Sleep function for rate limiting
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to format date to YYYY-MM-DD for comparison
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

async function findSuspiciousOrders() {
  console.log('🔍 Multi-strategy bot order detection...');
  console.log(`📅 Focus months: ${FOCUS_MONTHS.join(', ')}`);
  console.log(`🔧 Mode: ${DRY_RUN ? 'DRY RUN (no deletions)' : 'LIVE MODE (will delete data)'}`);
  
  try {
    // Get orders with date filtering if focusing on specific months
    console.log('📖 Fetching orders...');
    
    let whereClause = {};
    if (!ANALYZE_ALL_MONTHS && FOCUS_MONTHS.length > 0) {
      const dateConditions = FOCUS_MONTHS.map(month => {
        const startDate = new Date(`${month}-01T00:00:00.000Z`);
        const endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + 1);
        
        return {
          dateCreated: {
            gte: startDate,
            lt: endDate
          }
        };
      });
      
      whereClause = { OR: dateConditions };
    }
    
    const orders = await prisma.order.findMany({
      where: whereClause,
      include: {
        items: {
          select: {
            itemName: true,
            quantity: true,
            productId: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        dateCreated: 'desc'
      }
    });

    console.log(`📊 Found ${orders.length} orders in target period`);

    // Multiple detection strategies
    console.log('\n🔍 Analyzing with multiple detection strategies...');
    
    // Strategy 1: Exact duplicates (same customer, same day, same items)
    const exactGroups = new Map();
    
    // Strategy 2: Customer spam (same customer, many orders per day)
    const customerDayGroups = new Map();
    
    // Strategy 3: Product spam (same product, many times per day, any customer)
    const productDayGroups = new Map();
    
    for (const order of orders) {
      const date = formatDate(order.dateCreated);
      const customerId = order.customerId || `guest_${order.email}`;
      
      // Create item signature for exact matching
      const itemSignature = order.items
        .map(item => `${item.itemName || item.productId || 'unknown'}_${item.quantity}`)
        .sort()
        .join('|');
      
      // Strategy 1: Exact matching
      const exactKey = `${date}_${customerId}_${itemSignature}`;
      if (!exactGroups.has(exactKey)) {
        exactGroups.set(exactKey, []);
      }
      exactGroups.get(exactKey).push(order);
      
      // Strategy 2: Same customer, same day (any items)
      const customerDayKey = `${date}_${customerId}`;
      if (!customerDayGroups.has(customerDayKey)) {
        customerDayGroups.set(customerDayKey, []);
      }
      customerDayGroups.get(customerDayKey).push(order);
      
      // Strategy 3: Same product, same day (any customer) - process each item separately
      for (const item of order.items) {
        const productKey = item.itemName || `product_${item.productId}` || 'unknown_product';
        const productDayKey = `${date}_${productKey}`;
        
        if (!productDayGroups.has(productDayKey)) {
          productDayGroups.set(productDayKey, []);
        }
        productDayGroups.get(productDayKey).push({
          order: order,
          item: item
        });
      }
    }

    console.log(`📊 Exact patterns: ${exactGroups.size}`);
    console.log(`📊 Customer-day patterns: ${customerDayGroups.size}`);
    console.log(`📊 Product-day patterns: ${productDayGroups.size}`);

    // Analyze all strategies and find suspicious groups
    const allSuspiciousGroups = [];

    // Strategy 1: Find exact duplicates
    console.log('\n🔍 Strategy 1: Exact duplicate orders...');
    let exactCount = 0;
    for (const [key, groupOrders] of exactGroups) {
      if (groupOrders.length >= THRESHOLDS.EXACT_DUPLICATE) {
        allSuspiciousGroups.push({
          type: 'EXACT_DUPLICATE',
          key,
          orders: groupOrders,
          count: groupOrders.length,
          suspicionLevel: 'HIGH'
        });
        exactCount++;
      }
    }
    console.log(`   Found ${exactCount} exact duplicate groups`);

    // Strategy 2: Same customer, many orders same day
    console.log('🔍 Strategy 2: Customer spam (same customer, many orders per day)...');
    let customerSpamCount = 0;
    for (const [key, groupOrders] of customerDayGroups) {
      if (groupOrders.length >= THRESHOLDS.CUSTOMER_SPAM) {
        allSuspiciousGroups.push({
          type: 'CUSTOMER_SPAM',
          key,
          orders: groupOrders,
          count: groupOrders.length,
          suspicionLevel: groupOrders.length >= THRESHOLDS.CRITICAL_SPAM ? 'CRITICAL' : 'HIGH'
        });
        customerSpamCount++;
      }
    }
    console.log(`   Found ${customerSpamCount} customer spam groups`);

    // Strategy 3: Same product, many times per day
    console.log('🔍 Strategy 3: Product spam (same products, many times per day)...');
    let productSpamCount = 0;
    for (const [key, groupItems] of productDayGroups) {
      if (groupItems.length >= THRESHOLDS.PRODUCT_SPAM) {
        // Extract unique orders from the group items
        const uniqueOrders = Array.from(new Set(groupItems.map(item => item.order.id)))
          .map(orderId => groupItems.find(item => item.order.id === orderId).order);
        
        allSuspiciousGroups.push({
          type: 'PRODUCT_SPAM',
          key,
          orders: uniqueOrders,
          count: groupItems.length, // Total item count
          uniqueOrderCount: uniqueOrders.length,
          suspicionLevel: groupItems.length >= THRESHOLDS.CRITICAL_SPAM ? 'CRITICAL' : 'HIGH'
        });
        productSpamCount++;
      }
    }
    console.log(`   Found ${productSpamCount} product spam groups`);

    return allSuspiciousGroups;

  } catch (error) {
    console.error('❌ Error analyzing orders:', error);
    throw error;
  }
}

async function displaySuspiciousGroups(suspiciousGroups) {
  if (suspiciousGroups.length === 0) {
    console.log('\n✅ No suspicious orders found!');
    return;
  }

  // Sort by suspicion level and count
  suspiciousGroups.sort((a, b) => {
    if (a.suspicionLevel !== b.suspicionLevel) {
      return a.suspicionLevel === 'CRITICAL' ? -1 : 1;
    }
    return b.count - a.count;
  });

  const totalOrders = suspiciousGroups.reduce((sum, group) => sum + group.orders.length, 0);
  const criticalGroups = suspiciousGroups.filter(g => g.suspicionLevel === 'CRITICAL');
  
  console.log(`\n🚨 SUSPICIOUS ORDER ANALYSIS`);
  console.log('=' .repeat(50));
  console.log(`🔍 Total suspicious groups: ${suspiciousGroups.length}`);
  console.log(`📦 Total orders to delete: ${totalOrders}`);
  console.log(`🚨 Critical groups: ${criticalGroups.length}`);
  
  // Group by type
  const byType = suspiciousGroups.reduce((acc, group) => {
    acc[group.type] = (acc[group.type] || 0) + 1;
    return acc;
  }, {});
  
  console.log('\n📊 By detection type:');
  Object.entries(byType).forEach(([type, count]) => {
    console.log(`   ${type}: ${count} groups`);
  });

  // Display top suspicious groups
  console.log('\n📋 Top suspicious groups:');
  for (let i = 0; i < Math.min(suspiciousGroups.length, 15); i++) {
    const group = suspiciousGroups[i];
    const firstOrder = group.orders[0];
    const date = formatDate(firstOrder.dateCreated);
    
    const customerInfo = firstOrder.customer 
      ? `${firstOrder.customer.name} (${firstOrder.customer.email})`
      : `Guest: ${firstOrder.email}`;
    
    console.log(`\n${i + 1}. [${group.suspicionLevel}] ${group.type}`);
    console.log(`   📅 Date: ${date}`);
    console.log(`   👤 Customer: ${customerInfo}`);
    
    if (group.type === 'PRODUCT_SPAM') {
      const productName = group.key.split('_').slice(1).join('_');
      console.log(`   📦 Product: ${productName}`);
      console.log(`   🔢 Total items: ${group.count} | Unique orders: ${group.uniqueOrderCount}`);
    } else {
      console.log(`   🔢 Orders: ${group.count}`);
      if (firstOrder.items.length > 0) {
        const itemSummary = firstOrder.items
          .map(item => `${item.itemName || 'Product'} (qty: ${item.quantity})`)
          .join(', ');
        console.log(`   📦 Items: ${itemSummary}`);
      }
    }
    
    const totalValue = group.orders.reduce((sum, order) => sum + parseFloat(order.totalAmount), 0);
    console.log(`   💰 Total value: $${totalValue.toFixed(2)}`);
    console.log(`   🆔 Order IDs: ${group.orders.map(o => o.id).slice(0, 8).join(', ')}${group.orders.length > 8 ? '...' : ''}`);
  }

  if (suspiciousGroups.length > 15) {
    console.log(`\n... and ${suspiciousGroups.length - 15} more groups`);
  }
}

// Main function
async function main() {
  try {
    const suspiciousGroups = await findSuspiciousOrders();
    await displaySuspiciousGroups(suspiciousGroups);
    
    if (suspiciousGroups.length > 0 && !DRY_RUN) {
      console.log('\n⚠️  WARNING: About to delete suspicious orders!');
      console.log('🔄 Press Ctrl+C to cancel, or wait 10 seconds to continue...');
      await sleep(10000);
      
      // TODO: Implement deletion logic here
      console.log('🗑️  Deletion logic not implemented yet - add if needed');
    } else if (suspiciousGroups.length > 0) {
      console.log('\n🔧 DRY RUN MODE - No data will be deleted');
      console.log('💡 Set DRY_RUN = false to enable deletion');
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
