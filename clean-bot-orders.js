const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['error'],
  errorFormat: 'minimal'
});

// Configuration
const DUPLICATE_THRESHOLD = 5; // Delete if same order pattern appears 5+ times in same day
const DRY_RUN = true; // Set to false to actually delete data

// Sleep function for rate limiting
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to format date to YYYY-MM-DD for comparison
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

// Function to create a unique key for identifying duplicate patterns
function createOrderKey(order) {
  const date = formatDate(order.dateCreated);
  const customerId = order.customerId || 'guest';

  // Create a key based on items in the order
  const itemKeys = order.items
    .map(item => `${item.itemName || 'unknown'}_${item.quantity}`)
    .sort()
    .join('|');

  return `${date}_${customerId}_${itemKeys}`;
}

// Function to analyze order patterns for bot behavior
function analyzeOrderPattern(orders) {
  const analysis = {
    timeSpread: 0,
    avgTimeBetween: 0,
    sameAmounts: 0,
    suspiciousScore: 0
  };

  if (orders.length < 2) return analysis;

  // Sort by creation time
  const sortedOrders = orders.sort((a, b) => new Date(a.dateCreated) - new Date(b.dateCreated));

  // Calculate time spread and intervals
  const firstTime = new Date(sortedOrders[0].dateCreated);
  const lastTime = new Date(sortedOrders[sortedOrders.length - 1].dateCreated);
  analysis.timeSpread = (lastTime - firstTime) / (1000 * 60); // minutes

  // Calculate average time between orders
  let totalInterval = 0;
  for (let i = 1; i < sortedOrders.length; i++) {
    const interval = new Date(sortedOrders[i].dateCreated) - new Date(sortedOrders[i-1].dateCreated);
    totalInterval += interval;
  }
  analysis.avgTimeBetween = totalInterval / (sortedOrders.length - 1) / (1000 * 60); // minutes

  // Check for identical amounts
  const amounts = orders.map(o => parseFloat(o.totalAmount));
  const uniqueAmounts = new Set(amounts);
  analysis.sameAmounts = amounts.length - uniqueAmounts.size;

  // Calculate suspicion score (higher = more suspicious)
  analysis.suspiciousScore = 0;

  // Very short time spread with many orders = suspicious
  if (analysis.timeSpread < 60 && orders.length >= 5) analysis.suspiciousScore += 3;
  if (analysis.timeSpread < 30 && orders.length >= 3) analysis.suspiciousScore += 2;

  // Very regular intervals = suspicious
  if (analysis.avgTimeBetween < 5 && orders.length >= 5) analysis.suspiciousScore += 3;
  if (analysis.avgTimeBetween < 10 && orders.length >= 3) analysis.suspiciousScore += 2;

  // All same amounts = suspicious
  if (analysis.sameAmounts === orders.length - 1) analysis.suspiciousScore += 2;

  // High volume = suspicious
  if (orders.length >= 10) analysis.suspiciousScore += 2;
  if (orders.length >= 20) analysis.suspiciousScore += 3;

  return analysis;
}

async function findDuplicateOrders() {
  console.log('🔍 Analyzing orders for bot-generated duplicates...');
  console.log(`📊 Threshold: ${DUPLICATE_THRESHOLD}+ identical orders on same day`);
  console.log(`🔧 Mode: ${DRY_RUN ? 'DRY RUN (no deletions)' : 'LIVE MODE (will delete data)'}`);

  try {
    // Get all orders with their items
    console.log('📖 Fetching all orders with items...');
    const orders = await prisma.order.findMany({
      include: {
        items: {
          select: {
            itemName: true,
            quantity: true,
            productNetRevenue: true,
            productGrossRevenue: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        dateCreated: 'desc'
      }
    });

    console.log(`📊 Found ${orders.length} total orders to analyze`);

    // Group orders by pattern (date + customer + items)
    const orderGroups = new Map();

    for (const order of orders) {
      const key = createOrderKey(order);

      if (!orderGroups.has(key)) {
        orderGroups.set(key, []);
      }

      orderGroups.get(key).push(order);
    }

    console.log(`📊 Found ${orderGroups.size} unique order patterns`);

    // Find groups with duplicates above threshold and analyze them
    const duplicateGroups = [];
    let totalDuplicateOrders = 0;

    for (const [key, groupOrders] of orderGroups) {
      if (groupOrders.length >= DUPLICATE_THRESHOLD) {
        // Analyze the pattern for bot behavior
        const analysis = analyzeOrderPattern(groupOrders);

        duplicateGroups.push({
          key,
          orders: groupOrders,
          count: groupOrders.length,
          analysis
        });
        totalDuplicateOrders += groupOrders.length;
      }
    }

    // Sort by suspicion score (highest first)
    duplicateGroups.sort((a, b) => b.analysis.suspiciousScore - a.analysis.suspiciousScore);

    console.log(`\n🚨 Found ${duplicateGroups.length} suspicious order groups:`);
    console.log(`📊 Total orders to be deleted: ${totalDuplicateOrders}`);

    // Display details of duplicate groups
    for (const group of duplicateGroups.slice(0, 10)) { // Show first 10 groups
      const firstOrder = group.orders[0];
      const date = formatDate(firstOrder.dateCreated);
      const customerInfo = firstOrder.customer
        ? `${firstOrder.customer.name} (ID: ${firstOrder.customer.id})`
        : `Guest Customer (ID: ${firstOrder.customerId})`;

      const itemSummary = firstOrder.items
        .map(item => `${item.itemName} (qty: ${item.quantity})`)
        .join(', ');

      console.log(`\n🔍 Group ${duplicateGroups.indexOf(group) + 1}:`);
      console.log(`   📅 Date: ${date}`);
      console.log(`   👤 Customer: ${customerInfo}`);
      console.log(`   📦 Items: ${itemSummary}`);
      console.log(`   🔢 Count: ${group.count} identical orders`);
      console.log(`   ⏱️  Time spread: ${group.analysis.timeSpread.toFixed(2)} minutes`);
      console.log(`   ⏱️  Avg time between orders: ${group.analysis.avgTimeBetween.toFixed(2)} minutes`);
      console.log(`   💰 Same amount orders: ${group.analysis.sameAmounts}`);
      console.log(`   ⚠️  Suspicion score: ${group.analysis.suspiciousScore}/10`);
      console.log(`   🆔 Order IDs: ${group.orders.map(o => o.id).slice(0, 5).join(', ')}${group.count > 5 ? '...' : ''}`);
    }

    if (duplicateGroups.length > 10) {
      console.log(`\n... and ${duplicateGroups.length - 10} more groups`);
    }

    return duplicateGroups;

  } catch (error) {
    console.error('❌ Error analyzing orders:', error);
    throw error;
  }
}

async function deleteDuplicateOrders(duplicateGroups) {
  if (DRY_RUN) {
    console.log('\n🔧 DRY RUN MODE - No data will be deleted');
    console.log('💡 Set DRY_RUN = false to actually delete the data');
    return;
  }

  console.log('\n🗑️  Starting deletion of duplicate orders...');
  
  let deletedCount = 0;
  let failedCount = 0;
  const failedDeletions = [];

  for (const group of duplicateGroups) {
    console.log(`\n🔄 Processing group with ${group.count} orders...`);
    
    for (const order of group.orders) {
      try {
        // Delete order items first (due to foreign key constraints)
        await prisma.orderItem.deleteMany({
          where: { orderId: order.id }
        });

        // Delete the order
        await prisma.order.delete({
          where: { id: order.id }
        });

        deletedCount++;
        console.log(`✅ Deleted order #${order.id}`);

        // Rate limiting - small delay every 10 deletions
        if (deletedCount % 10 === 0) {
          await sleep(100);
        }

      } catch (error) {
        failedCount++;
        failedDeletions.push({ orderId: order.id, error: error.message });
        console.log(`❌ Failed to delete order #${order.id}: ${error.message}`);
      }
    }
  }

  console.log(`\n🎉 Cleanup complete:`);
  console.log(`   ✅ Successfully deleted: ${deletedCount} orders`);
  console.log(`   ❌ Failed: ${failedCount} orders`);

  if (failedDeletions.length > 0) {
    console.log(`\n📝 Failed deletions saved to failed_deletions.txt`);
    const fs = require('fs');
    fs.writeFileSync('failed_deletions.txt', 
      failedDeletions.map(f => `${f.orderId}: ${f.error}`).join('\n')
    );
  }
}

// Function to generate summary report
function generateSummaryReport(duplicateGroups) {
  console.log('\n📊 SUMMARY REPORT');
  console.log('=' .repeat(50));

  if (duplicateGroups.length === 0) {
    console.log('✅ No suspicious duplicate orders found!');
    return;
  }

  const totalOrders = duplicateGroups.reduce((sum, group) => sum + group.count, 0);
  const highSuspicionGroups = duplicateGroups.filter(g => g.analysis.suspiciousScore >= 5);
  const mediumSuspicionGroups = duplicateGroups.filter(g => g.analysis.suspiciousScore >= 3 && g.analysis.suspiciousScore < 5);

  console.log(`🔍 Total suspicious groups: ${duplicateGroups.length}`);
  console.log(`📦 Total orders to delete: ${totalOrders}`);
  console.log(`🚨 High suspicion (5+ score): ${highSuspicionGroups.length} groups`);
  console.log(`⚠️  Medium suspicion (3-4 score): ${mediumSuspicionGroups.length} groups`);

  // Customer analysis
  const customerCounts = new Map();
  duplicateGroups.forEach(group => {
    const customerId = group.orders[0].customerId || 'guest';
    customerCounts.set(customerId, (customerCounts.get(customerId) || 0) + group.count);
  });

  const topCustomers = Array.from(customerCounts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5);

  console.log('\n👤 Top customers with suspicious orders:');
  topCustomers.forEach(([customerId, count], index) => {
    console.log(`   ${index + 1}. Customer ${customerId}: ${count} orders`);
  });
}

async function cleanBotOrders() {
  console.log('🤖 Starting bot order cleanup script...');
  console.log('=' .repeat(60));

  try {
    // Find duplicate orders
    const duplicateGroups = await findDuplicateOrders();

    // Generate summary report
    generateSummaryReport(duplicateGroups);

    if (duplicateGroups.length === 0) {
      return;
    }

    // Ask for confirmation if not in dry run mode
    if (!DRY_RUN) {
      console.log('\n⚠️  WARNING: This will permanently delete data!');
      console.log('🔄 Press Ctrl+C to cancel, or wait 10 seconds to continue...');
      await sleep(10000);
    }

    // Delete duplicate orders
    await deleteDuplicateOrders(duplicateGroups);

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

// Main function
async function main() {
  try {
    await cleanBotOrders();
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
