import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { OrderQueryDto, OrderSortBy } from './dto/order-query.dto';
import { 
  OrderDto, 
  PaginatedOrdersDto, 
  PaginationDto,
  OrderItemDto 
} from './dto/order-response.dto';
import { 
  OrderAnalyticsDto,
  OrderReportsDto,
  OrderInsightsDto,
  DailyOrderStatsDto,
  MonthlyOrderStatsDto,
  YearlyOrderStatsDto,
  TopProductsDto,
  CustomerInsightsDto
} from './dto/order-analytics.dto';

@Injectable()
export class OrdersService {
  constructor(private prisma: PrismaService) {}

  async getAllOrders(query: OrderQueryDto): Promise<PaginatedOrdersDto> {
    const { page = 1, limit = 30, search, sortBy, status, dateFrom, dateTo, minAmount, maxAmount, customerId } = query;
    
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { id: isNaN(Number(search)) ? undefined : Number(search) },
      ].filter(condition => condition.id !== undefined || condition.name || condition.email);
    }

    if (status) {
      where.status = status;
    }

    if (customerId) {
      where.customerId = customerId;
    }

    if (dateFrom || dateTo) {
      where.dateCreated = {};
      if (dateFrom) {
        where.dateCreated.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.dateCreated.lte = new Date(dateTo);
      }
    }

    if (minAmount !== undefined || maxAmount !== undefined) {
      where.totalAmount = {};
      if (minAmount !== undefined) {
        where.totalAmount.gte = minAmount;
      }
      if (maxAmount !== undefined) {
        where.totalAmount.lte = maxAmount;
      }
    }

    // Build order by clause
    const orderBy = this.buildOrderByClause(sortBy);

    // Get total count
    const total = await this.prisma.order.count({ where });

    // Get orders with related data
    const orders = await this.prisma.order.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        coupon: {
          select: {
            id: true,
            code: true,
            description: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              },
            },
          },
        },
      },
      orderBy,
      skip,
      take,
    });

    // Format orders
    const formattedOrders = orders.map(order => this.formatOrder(order));

    const pagination: PaginationDto = {
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      itemsPerPage: limit,
      hasNextPage: page < Math.ceil(total / limit),
      hasPreviousPage: page > 1,
    };

    return {
      data: formattedOrders,
      pagination,
      appliedFilters: query,
    };
  }

  async getOrderById(id: number): Promise<OrderDto> {
    const order = await this.prisma.order.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        coupon: {
          select: {
            id: true,
            code: true,
            description: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              },
            },
          },
        },
      },
    });

    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    return this.formatOrder(order);
  }

  private buildOrderByClause(sortBy: OrderSortBy = OrderSortBy.DATE_CREATED_DESC): any {
    switch (sortBy) {
      case OrderSortBy.DATE_CREATED_ASC:
        return { dateCreated: 'asc' };
      case OrderSortBy.DATE_CREATED_DESC:
        return { dateCreated: 'desc' };
      case OrderSortBy.TOTAL_AMOUNT_ASC:
        return { totalAmount: 'asc' };
      case OrderSortBy.TOTAL_AMOUNT_DESC:
        return { totalAmount: 'desc' };
      case OrderSortBy.STATUS:
        return { status: 'asc' };
      case OrderSortBy.CUSTOMER_NAME:
        return { name: 'asc' };
      default:
        return { dateCreated: 'desc' };
    }
  }

  private formatOrder(order: any): OrderDto {
    return {
      id: order.id,
      customerId: order.customerId,
      name: order.name,
      email: order.email,
      firstName: order.firstName,
      lastName: order.lastName,
      companyName: order.companyName,
      country: order.country,
      streetAddress: order.streetAddress,
      city: order.city,
      state: order.state,
      zipCode: order.zipCode,
      phone: order.phone,
      shippingName: order.shippingName,
      shippingEmail: order.shippingEmail,
      shippingFirstName: order.shippingFirstName,
      shippingLastName: order.shippingLastName,
      shippingCompany: order.shippingCompany,
      shippingCountry: order.shippingCountry,
      shippingStreet: order.shippingStreet,
      shippingCity: order.shippingCity,
      shippingState: order.shippingState,
      shippingZipCode: order.shippingZipCode,
      shippingPhone: order.shippingPhone,
      dateCreated: order.dateCreated,
      couponId: order.couponId,
      couponAmount: Number(order.couponAmount),
      taxAmount: Number(order.taxAmount),
      shippingAmount: Number(order.shippingAmount),
      shippingTaxAmount: Number(order.shippingTaxAmount),
      totalAmount: Number(order.totalAmount),
      status: order.status,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      customer: order.customer,
      coupon: order.coupon,
      items: order.items.map(item => this.formatOrderItem(item)),
    };
  }

  private formatOrderItem(item: any): OrderItemDto {
    return {
      id: item.id,
      orderId: item.orderId,
      productId: item.productId,
      variationId: item.variationId,
      quantity: item.quantity,
      productNetRevenue: Number(item.productNetRevenue),
      productGrossRevenue: Number(item.productGrossRevenue),
      itemName: item.itemName,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      product: item.product,
    };
  }

  // Analytics Methods
  async getOrderAnalytics(dateFrom: Date, dateTo: Date): Promise<OrderAnalyticsDto> {
    const orders = await this.prisma.order.findMany({
      where: {
        dateCreated: {
          gte: dateFrom,
          lte: dateTo,
        },
      },
      select: {
        totalAmount: true,
        status: true,
      },
    });

    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + Number(order.totalAmount), 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    const statusCounts = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalOrders,
      totalRevenue,
      averageOrderValue,
      completedOrders: statusCounts.COMPLETED || 0,
      pendingOrders: statusCounts.PENDING || 0,
      cancelledOrders: statusCounts.CANCELLED || 0,
      refundedOrders: statusCounts.REFUNDED || 0,
      processingOrders: statusCounts.PROCESSING || 0,
    };
  }

  async getLastDayAnalytics(): Promise<OrderAnalyticsDto & { dailyStats: DailyOrderStatsDto[] }> {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const analytics = await this.getOrderAnalytics(yesterday, now);
    const dailyStats = await this.getDailyStats(yesterday, now);

    return { ...analytics, dailyStats };
  }

  async getLastMonthAnalytics(): Promise<OrderAnalyticsDto & { dailyStats: DailyOrderStatsDto[] }> {
    const now = new Date();
    const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const analytics = await this.getOrderAnalytics(lastMonth, now);
    const dailyStats = await this.getDailyStats(lastMonth, now);

    return { ...analytics, dailyStats };
  }

  async getLastYearAnalytics(): Promise<OrderAnalyticsDto & { monthlyStats: MonthlyOrderStatsDto[] }> {
    const now = new Date();
    const lastYear = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());

    const analytics = await this.getOrderAnalytics(lastYear, now);
    const monthlyStats = await this.getMonthlyStats(lastYear, now);

    return { ...analytics, monthlyStats };
  }

  async getLast5YearsAnalytics(): Promise<OrderAnalyticsDto & { yearlyStats: YearlyOrderStatsDto[] }> {
    const now = new Date();
    const fiveYearsAgo = new Date(now.getFullYear() - 5, now.getMonth(), now.getDate());

    const analytics = await this.getOrderAnalytics(fiveYearsAgo, now);
    const yearlyStats = await this.getYearlyStats(fiveYearsAgo, now);

    return { ...analytics, yearlyStats };
  }

  async getOrderReports(): Promise<OrderReportsDto> {
    const [lastDay, lastMonth, lastYear, last5Years] = await Promise.all([
      this.getLastDayAnalytics(),
      this.getLastMonthAnalytics(),
      this.getLastYearAnalytics(),
      this.getLast5YearsAnalytics(),
    ]);

    return {
      lastDay,
      lastMonth,
      lastYear,
      last5Years,
    };
  }

  private async getDailyStats(dateFrom: Date, dateTo: Date): Promise<DailyOrderStatsDto[]> {
    const result = await this.prisma.$queryRaw`
      SELECT
        DATE("dateCreated") as date,
        COUNT(*)::int as total_orders,
        SUM("totalAmount")::float as total_revenue,
        AVG("totalAmount")::float as average_order_value,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END)::int as completed_orders
      FROM "Order"
      WHERE "dateCreated" >= ${dateFrom} AND "dateCreated" <= ${dateTo}
      GROUP BY DATE("dateCreated")
      ORDER BY DATE("dateCreated")
    ` as any[];

    return result.map(row => ({
      date: row.date.toISOString().split('T')[0],
      totalOrders: row.total_orders,
      totalRevenue: row.total_revenue || 0,
      averageOrderValue: row.average_order_value || 0,
      completedOrders: row.completed_orders,
    }));
  }

  private async getMonthlyStats(dateFrom: Date, dateTo: Date): Promise<MonthlyOrderStatsDto[]> {
    const result = await this.prisma.$queryRaw`
      SELECT
        EXTRACT(YEAR FROM "dateCreated")::int as year,
        EXTRACT(MONTH FROM "dateCreated")::int as month,
        COUNT(*)::int as total_orders,
        SUM("totalAmount")::float as total_revenue,
        AVG("totalAmount")::float as average_order_value,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END)::int as completed_orders
      FROM "Order"
      WHERE "dateCreated" >= ${dateFrom} AND "dateCreated" <= ${dateTo}
      GROUP BY EXTRACT(YEAR FROM "dateCreated"), EXTRACT(MONTH FROM "dateCreated")
      ORDER BY year, month
    ` as any[];

    return result.map(row => ({
      year: row.year,
      month: `${row.year}-${String(row.month).padStart(2, '0')}`,
      totalOrders: row.total_orders,
      totalRevenue: row.total_revenue || 0,
      averageOrderValue: row.average_order_value || 0,
      completedOrders: row.completed_orders,
    }));
  }

  private async getYearlyStats(dateFrom: Date, dateTo: Date): Promise<YearlyOrderStatsDto[]> {
    const result = await this.prisma.$queryRaw`
      SELECT
        EXTRACT(YEAR FROM "dateCreated")::int as year,
        COUNT(*)::int as total_orders,
        SUM("totalAmount")::float as total_revenue,
        AVG("totalAmount")::float as average_order_value,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END)::int as completed_orders
      FROM "Order"
      WHERE "dateCreated" >= ${dateFrom} AND "dateCreated" <= ${dateTo}
      GROUP BY EXTRACT(YEAR FROM "dateCreated")
      ORDER BY year
    ` as any[];

    return result.map(row => ({
      year: row.year,
      totalOrders: row.total_orders,
      totalRevenue: row.total_revenue || 0,
      averageOrderValue: row.average_order_value || 0,
      completedOrders: row.completed_orders,
    }));
  }

  async getOrderInsights(dateFrom?: Date, dateTo?: Date): Promise<OrderInsightsDto> {
    const now = new Date();
    const defaultDateFrom = dateFrom || new Date(now.getFullYear(), 0, 1); // Start of current year
    const defaultDateTo = dateTo || now;

    const [analytics, topProducts, topCustomers, revenueByStatus] = await Promise.all([
      this.getOrderAnalytics(defaultDateFrom, defaultDateTo),
      this.getTopProducts(defaultDateFrom, defaultDateTo),
      this.getTopCustomers(defaultDateFrom, defaultDateTo),
      this.getRevenueByStatus(defaultDateFrom, defaultDateTo),
    ]);

    return {
      analytics,
      topProducts,
      topCustomers,
      revenueByStatus,
    };
  }

  private async getTopProducts(dateFrom: Date, dateTo: Date, limit: number = 10): Promise<TopProductsDto[]> {
    const result = await this.prisma.$queryRaw`
      SELECT
        oi."productId",
        COALESCE(p.name, oi."itemName") as product_name,
        SUM(oi.quantity)::int as total_quantity,
        SUM(oi."productGrossRevenue")::float as total_revenue,
        COUNT(DISTINCT oi."orderId")::int as order_count
      FROM "OrderItem" oi
      LEFT JOIN "Product" p ON oi."productId" = p.id
      INNER JOIN "Order" o ON oi."orderId" = o.id
      WHERE o."dateCreated" >= ${dateFrom} AND o."dateCreated" <= ${dateTo}
      GROUP BY oi."productId", p.name, oi."itemName"
      ORDER BY total_revenue DESC
      LIMIT ${limit}
    ` as any[];

    return result.map(row => ({
      productId: row.productId,
      productName: row.product_name || 'Unknown Product',
      totalQuantity: row.total_quantity,
      totalRevenue: row.total_revenue || 0,
      orderCount: row.order_count,
    }));
  }

  private async getTopCustomers(dateFrom: Date, dateTo: Date, limit: number = 10): Promise<CustomerInsightsDto[]> {
    const result = await this.prisma.$queryRaw`
      SELECT
        o."customerId",
        COALESCE(u.name, o.name) as customer_name,
        COALESCE(u.email, o.email) as customer_email,
        COUNT(o.id)::int as total_orders,
        SUM(o."totalAmount")::float as total_spent,
        AVG(o."totalAmount")::float as average_order_value,
        MAX(o."dateCreated") as last_order_date
      FROM "Order" o
      LEFT JOIN "User" u ON o."customerId" = u.id
      WHERE o."dateCreated" >= ${dateFrom} AND o."dateCreated" <= ${dateTo}
      GROUP BY o."customerId", u.name, u.email, o.name, o.email
      ORDER BY total_spent DESC
      LIMIT ${limit}
    ` as any[];

    return result.map(row => ({
      customerId: row.customerId,
      customerName: row.customer_name || 'Guest Customer',
      customerEmail: row.customer_email,
      totalOrders: row.total_orders,
      totalSpent: row.total_spent || 0,
      averageOrderValue: row.average_order_value || 0,
      lastOrderDate: row.last_order_date,
    }));
  }

  private async getRevenueByStatus(dateFrom: Date, dateTo: Date): Promise<{ status: string; count: number; revenue: number; }[]> {
    const result = await this.prisma.$queryRaw`
      SELECT
        status,
        COUNT(*)::int as count,
        SUM("totalAmount")::float as revenue
      FROM "Order"
      WHERE "dateCreated" >= ${dateFrom} AND "dateCreated" <= ${dateTo}
      GROUP BY status
      ORDER BY revenue DESC
    ` as any[];

    return result.map(row => ({
      status: row.status,
      count: row.count,
      revenue: row.revenue || 0,
    }));
  }
}
