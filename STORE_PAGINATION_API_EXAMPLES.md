# Store API Pagination Examples

This document provides examples of the paginated responses for the Store API endpoints.

## Query Parameters

Both endpoints now support the following query parameters:

- `page` (optional, default: 1) - Page number (minimum: 1)
- `limit` (optional, default: 20) - Number of items per page (minimum: 1, maximum: 100)
- `search` (optional) - Search term to filter products by name, SKU, short description, or description

## 1. Get All Products (Paginated with Search)

### Endpoint
```
GET /store/products?page=1&limit=10&search=coffee
```

### Example Response
```json
{
  "pagination": {
    "total": 3,
    "page": 1,
    "limit": 10,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  },
  "searchTerm": "coffee",
  "data": [
    {
      "id": 1,
      "name": "Premium Coffee Beans",
      "shortDescription": "High-quality arabica coffee beans from Colombia",
      "price": "24.99",
      "sku": "COFFEE-001",
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "type": "SIMPLE",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-20T14:45:00.000Z",
      "images": [
        {
          "url": "https://static.omgpackagings.com/products/coffee-beans-1.jpg"
        }
      ],
      "categories": [
        {
          "id": 1,
          "name": "Beverages"
        },
        {
          "id": 5,
          "name": "Coffee"
        }
      ],
      "tags": [
        {
          "id": 1,
          "name": "Premium"
        },
        {
          "id": 3,
          "name": "Organic"
        }
      ]
    },
    {
      "id": 2,
      "name": "Eco-Friendly Water Bottle",
      "shortDescription": "Reusable stainless steel water bottle",
      "price": "19.99",
      "sku": "BOTTLE-002",
      "stockQuantity": 25,
      "stockStatus": "IN_STOCK",
      "type": "SIMPLE",
      "createdAt": "2024-01-14T09:15:00.000Z",
      "updatedAt": "2024-01-19T16:20:00.000Z",
      "images": [
        {
          "url": "https://static.omgpackagings.com/products/water-bottle-1.jpg"
        }
      ],
      "categories": [
        {
          "id": 2,
          "name": "Accessories"
        }
      ],
      "tags": [
        {
          "id": 2,
          "name": "Eco-Friendly"
        },
        {
          "id": 4,
          "name": "Reusable"
        }
      ]
    }
  ]
}
```

## 2. Get Products by Category (Paginated with Search)

### Endpoint
```
GET /store/categories/1/products?page=1&limit=5&search=tea
```

### Example Response
```json
{
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 5,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  },
  "searchTerm": "tea",
  "data": [
    {
      "id": 6,
      "name": "Herbal Tea Collection",
      "shortDescription": "Assorted herbal teas for relaxation",
      "price": "15.99",
      "sku": "TEA-006",
      "stockQuantity": 30,
      "stockStatus": "IN_STOCK",
      "type": "GROUPED",
      "createdAt": "2024-01-10T11:00:00.000Z",
      "updatedAt": "2024-01-18T13:30:00.000Z",
      "images": [
        {
          "url": "https://static.omgpackagings.com/products/herbal-tea-1.jpg"
        }
      ],
      "categories": [
        {
          "id": 1,
          "name": "Beverages"
        }
      ],
      "tags": [
        {
          "id": 5,
          "name": "Herbal"
        },
        {
          "id": 6,
          "name": "Relaxation"
        }
      ],
      "ProductAttribute": [
        {
          "id": 1,
          "attribute": {
            "id": 1,
            "name": "Flavor"
          },
          "values": [
            {
              "id": 1,
              "value": "Chamomile"
            },
            {
              "id": 2,
              "value": "Peppermint"
            }
          ]
        }
      ],
      "variants": [
        {
          "id": 1,
          "sku": "TEA-006-CHAM",
          "price": "15.99",
          "stockQuantity": 15,
          "stockStatus": "IN_STOCK",
          "ProductImage": [
            {
              "url": "https://static.omgpackagings.com/products/chamomile-tea.jpg"
            }
          ],
          "attributes": [
            {
              "value": {
                "id": 1,
                "value": "Chamomile"
              }
            }
          ]
        }
      ]
    }
  ],
  "category": {
    "id": 1,
    "name": "Beverages"
  }
}
```

## Response Structure

### Pagination Information

The `pagination` object contains:

- `total`: Total number of products available (filtered by search if applicable)
- `page`: Current page number
- `limit`: Number of items per page
- `totalPages`: Total number of pages available
- `hasNext`: Boolean indicating if there's a next page
- `hasPrev`: Boolean indicating if there's a previous page

### Search Information

- `searchTerm`: The search term that was applied (null if no search was performed)

## Search Functionality

The search parameter allows you to filter products by:

1. **Product Name** - Case-insensitive partial matching
2. **SKU** - Case-insensitive partial matching
3. **Short Description** - Case-insensitive partial matching
4. **Description** - Case-insensitive partial matching

The search uses `ILIKE` (case-insensitive LIKE) with partial matching, so searching for "coffee" will match:
- "Premium Coffee Beans"
- "COFFEE-001"
- "Best coffee in town"
- "This product contains coffee extract"

## Usage Examples

### Get first page with default limit (20 items)
```
GET /store/products
```

### Get specific page with custom limit
```
GET /store/products?page=3&limit=15
```

### Search products by name or SKU
```
GET /store/products?search=coffee
GET /store/products?search=COFFEE-001
GET /store/products?search=premium
```

### Search with pagination
```
GET /store/products?search=coffee&page=1&limit=5
```

### Get products from a specific category with pagination
```
GET /store/categories/5/products?page=1&limit=25
```

### Search within a specific category
```
GET /store/categories/1/products?search=herbal&page=1&limit=10
```

### Navigate through pages
```
# First page
GET /store/products?page=1&limit=10

# Next page (if hasNext is true)
GET /store/products?page=2&limit=10

# Previous page (if hasPrev is true)
GET /store/products?page=1&limit=10
```

### Search Examples by Field
```
# Search by product name
GET /store/products?search=Premium Coffee

# Search by SKU
GET /store/products?search=COFFEE-001

# Search by description content
GET /store/products?search=arabica

# Search is case-insensitive
GET /store/products?search=PREMIUM
GET /store/products?search=premium
GET /store/products?search=Premium
```

## Error Responses

### Invalid page number (less than 1)
```json
{
  "statusCode": 400,
  "message": [
    "page must not be less than 1"
  ],
  "error": "Bad Request"
}
```

### Invalid limit (greater than 100)
```json
{
  "statusCode": 400,
  "message": [
    "limit must not be greater than 100"
  ],
  "error": "Bad Request"
}
```

### Category not found
```json
{
  "statusCode": 404,
  "message": "Category with ID 999 not found",
  "error": "Not Found"
}
```

## Debugging

If you're experiencing issues with the API, debugging has been added to help identify parsing problems:

### Server-Side Debugging

The API now includes comprehensive logging that will show:

1. **Raw Query Parameters**: What the server receives from the request
2. **Parameter Types**: The JavaScript types of each parameter
3. **Transformation Process**: How class-transformer processes the values
4. **Validation Results**: Any validation errors or successes
5. **Database Queries**: The actual Prisma queries being executed
6. **Response Data**: The final response structure

### Debug Output Example

When you make a request like `GET /store/products?page=2&limit=5&search=coffee`, you'll see logs like:

```
🔍 [Debug Middleware] Store API Request:
📡 Method: GET
📡 URL: /store/products?page=2&limit=5&search=coffee
📡 Query Params (raw): { page: '2', limit: '5', search: 'coffee' }

🔍 [DTO Transform] page value: 2 type: number
🔍 [DTO Transform] limit value: 5 type: number
🔍 [DTO Transform] search value: coffee type: string

🔍 [Store Controller] getAllProducts - Processed params: {
  page: 2,
  limit: 5,
  search: 'coffee'
}

🔍 [Store Service] getAllProducts - Where clause: {
  "OR": [
    { "name": { "contains": "coffee", "mode": "insensitive" } },
    { "sku": { "contains": "coffee", "mode": "insensitive" } }
  ]
}
```

### Testing Script

A test script `test-store-api.js` has been created to help debug API calls:

```bash
# Run the test script (make sure your server is running on port 3000)
node test-store-api.js
```

This script will test various scenarios including:
- Basic pagination
- Search functionality
- Invalid parameters
- Edge cases
- Error conditions
