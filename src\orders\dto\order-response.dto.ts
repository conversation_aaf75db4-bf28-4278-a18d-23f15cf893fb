export class OrderItemDto {
  id: number;
  orderId: number;
  productId?: number;
  variationId?: number;
  quantity: number;
  productNetRevenue: number;
  productGrossRevenue: number;
  itemName?: string; // For legacy items
  createdAt: Date;
  updatedAt: Date;
  product?: {
    id: number;
    name: string;
    sku?: string;
  };
}

export class OrderDto {
  id: number;
  customerId?: number;
  name: string;
  email: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  country?: string;
  streetAddress?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  phone?: string;
  shippingName?: string;
  shippingEmail?: string;
  shippingFirstName?: string;
  shippingLastName?: string;
  shippingCompany?: string;
  shippingCountry?: string;
  shippingStreet?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingPhone?: string;
  dateCreated: Date;
  couponId?: number;
  couponAmount: number;
  taxAmount: number;
  shippingAmount: number;
  shippingTaxAmount: number;
  totalAmount: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  customer?: {
    id: number;
    name: string;
    email: string;
  };
  coupon?: {
    id: number;
    code: string;
    description?: string;
  };
  items: OrderItemDto[];
}

export class PaginationDto {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export class PaginatedOrdersDto {
  data: OrderDto[];
  pagination: PaginationDto;
  appliedFilters: any;
}
