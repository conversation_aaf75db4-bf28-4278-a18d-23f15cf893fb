import { ProductType, AccessLevel } from '@prisma/client';

export class ProductImageDto {
  id: number;
  url: string;
  position: number;
}

export class CategoryDto {
  id: number;
  name: string;
  slug: string;
  imageUrl?: string;
}

export class TagDto {
  id: number;
  name: string;
  slug: string;
}

export class ListingDto {
  id: number;
  title: string;
  content: string;
}

export class AttributeValueDto {
  id: number;
  value: string;
}

export class AttributeDto {
  id: number;
  name: string;
  values: AttributeValueDto[];
}

export class VariantAttributeDto {
  value: {
    id: number;
    value: string;
  };
}

export class VariantDto {
  id: number;
  sku: string;
  price: number;
  stockQuantity: number | null;
  stockStatus: string;
  ProductImage: ProductImageDto[];
  attributes: VariantAttributeDto[];
}

export class ProductDetailsDto {
  id: number;
  sku: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  salePrice: number | null;
  stockQuantity: number | null;
  stockStatus: string;
  type: ProductType;
  access: AccessLevel;
  createdAt: Date;
  updatedAt: Date;
  images: ProductImageDto[];
  categories: CategoryDto[];
  tags: TagDto[];
  listings: ListingDto[];
  ProductAttribute: {
    attribute: AttributeDto;
    values: AttributeValueDto[];
  }[];
  variants: VariantDto[];
}