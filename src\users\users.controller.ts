import { 
  Controller, 
  Get, 
  Query, 
  Param, 
  ParseIntPipe, 
  UseGuards,
  NotFoundException 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UsersService } from './users.service';
import { 
  UserPaginationQueryDto, 
  PaginatedUsersResponseDto,
  UserListItemDto 
} from './dto/user-pagination.dto';

@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @Roles('admin', 'super-admin')
  async getAllUsers(
    @Query() query: UserPaginationQueryDto
  ): Promise<PaginatedUsersResponseDto> {
    return this.usersService.getAllUsers(query);
  }

  @Get(':id')
  @Roles('admin', 'super-admin')
  async getUserById(
    @Param('id', ParseIntPipe) id: number
  ): Promise<UserListItemDto> {
    const user = await this.usersService.getUserById(id);
    
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    
    return user;
  }
}
