// Test script for Users API endpoints
const baseUrl = 'http://localhost:3000';

// You'll need to replace this with a valid admin JWT token
const adminToken = 'YOUR_ADMIN_JWT_TOKEN_HERE';

async function testUsersAPI(endpoint, description) {
  console.log(`\n🧪 Testing: ${description}`);
  console.log(`📡 URL: ${baseUrl}${endpoint}`);
  
  try {
    const response = await fetch(`${baseUrl}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    console.log(`✅ Status: ${response.status}`);
    
    if (response.status === 200) {
      if (data.pagination) {
        // Paginated response
        console.log(`📊 Pagination:`, {
          total: data.pagination.total,
          page: data.pagination.page,
          limit: data.pagination.limit,
          totalPages: data.pagination.totalPages,
          hasNext: data.pagination.hasNext,
          hasPrev: data.pagination.hasPrev
        });
        
        console.log(`👥 Users Count: ${data.data.length}`);
        
        if (data.searchTerm) {
          console.log(`🔍 Search Term: "${data.searchTerm}"`);
        }
        
        if (data.data.length > 0) {
          console.log(`📝 Sample User:`, {
            id: data.data[0].id,
            name: data.data[0].name,
            email: data.data[0].email,
            isAdmin: data.data[0].isAdmin,
            isVerified: data.data[0].isVerified,
            createdAt: data.data[0].createdAt
          });
        }
      } else {
        // Single user response
        console.log(`👤 User Details:`, {
          id: data.id,
          name: data.name,
          email: data.email,
          isAdmin: data.isAdmin,
          isSuperAdmin: data.isSuperAdmin,
          isVerified: data.isVerified,
          points: data.points,
          createdAt: data.createdAt
        });
      }
    } else {
      console.log(`❌ Error Response:`, data);
    }
    
  } catch (error) {
    console.log(`❌ Request Error:`, error.message);
  }
}

async function runUsersTests() {
  console.log('🚀 Starting Users API Tests...');
  console.log('⚠️  Make sure to replace adminToken with a valid JWT token');
  
  if (adminToken === 'YOUR_ADMIN_JWT_TOKEN_HERE') {
    console.log('❌ Please update the adminToken variable with a valid JWT token');
    return;
  }
  
  // Test basic pagination
  await testUsersAPI('/api/users', 'Get all users (default pagination)');
  await testUsersAPI('/api/users?page=1&limit=5', 'Get users (page 1, limit 5)');
  await testUsersAPI('/api/users?page=2&limit=10', 'Get users (page 2, limit 10)');
  
  // Test search functionality
  await testUsersAPI('/api/users?search=admin', 'Search users for "admin"');
  await testUsersAPI('/api/users?search=@gmail.com', 'Search users with Gmail addresses');
  await testUsersAPI('/api/users?search=john', 'Search users for "john"');
  
  // Test search with pagination
  await testUsersAPI('/api/users?search=test&page=1&limit=3', 'Search "test" with pagination');
  
  // Test single user endpoint (you may need to adjust the ID)
  await testUsersAPI('/api/users/1', 'Get user by ID (1)');
  await testUsersAPI('/api/users/2', 'Get user by ID (2)');
  
  // Test edge cases
  await testUsersAPI('/api/users?page=0', 'Invalid page (0)');
  await testUsersAPI('/api/users?limit=101', 'Invalid limit (101)');
  await testUsersAPI('/api/users?page=abc', 'Invalid page (abc)');
  await testUsersAPI('/api/users/999999', 'Non-existent user ID');
  
  console.log('\n✅ All users API tests completed!');
}

// Instructions for getting admin token
console.log(`
📋 Instructions to get admin JWT token:

1. Login as admin user via POST /auth/login
2. Copy the 'access_token' from the response
3. Replace 'YOUR_ADMIN_JWT_TOKEN_HERE' in this script
4. Run the script again

Example login request:
curl -X POST http://localhost:3000/api/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email":"<EMAIL>","password":"your_password"}'
`);

// Run the tests
runUsersTests().catch(console.error);
